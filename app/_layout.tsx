import "~/global.css";

import {
  DarkTheme,
  DefaultTheme,
  Theme,
  ThemeProvider,
} from "@react-navigation/native";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React from "react";

import { NAV_THEME } from "~/lib/constants";
import { useColorScheme } from "~/lib/useColorScheme";
import { PortalHost } from "@rn-primitives/portal";
import { ThemeToggle } from "~/components/ThemeToggle";
import { setAndroidNavigationBar } from "~/lib/android-navigation-bar";
import { QueryClient } from "@tanstack/react-query";

import { RootSiblingParent } from "react-native-root-siblings";

import { KeyboardProvider } from "react-native-keyboard-controller";

import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { clientPersister } from "~/lib/query-cache";
import { AuthContextProvider } from "~/modules/login/auth-provider";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { FontScaleProvider } from "~/lib/font-scale-context";
import { PermissionProvider } from "~/modules/permissions";
import { Appearance, Platform, View } from "react-native";

import * as Notifications from "expo-notifications";

import * as TaskManager from "expo-task-manager";
import { NotificationProvider } from "~/contexts/NotificationContext";
import { NotificationManager } from "~/components/notifications/NotificationManager";

const LIGHT_THEME: Theme = {
  ...DefaultTheme,
  colors: NAV_THEME.light,
};
const DARK_THEME: Theme = {
  ...DarkTheme,
  colors: NAV_THEME.dark,
};

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from "expo-router";

const usePlatformSpecificSetup = Platform.select({
  web: useSetWebBackgroundClassName,
  android: useSetAndroidNavigationBar,
  default: noop,
});

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: false,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

const BACKGROUND_NOTIFICATION_TASK = "BACKGROUND-NOTIFICATION-TASK";

TaskManager.defineTask(
  BACKGROUND_NOTIFICATION_TASK,
  async ({ data, error, executionInfo }) => {
    console.log("✅ Received a notification in the background!", {
      data,
      error,
      executionInfo,
    });
    // Do something with the notification data
    return Promise.resolve();
  }
);

Notifications.registerTaskAsync(BACKGROUND_NOTIFICATION_TASK);

function InitialLayout() {
  usePlatformSpecificSetup();

  const { isDarkColorScheme } = useColorScheme();

  return (
    <ThemeProvider value={isDarkColorScheme ? DARK_THEME : LIGHT_THEME}>
      <StatusBar style={isDarkColorScheme ? "light" : "dark"} />
      <Stack>
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
            headerRight: () => <ThemeToggle />,
          }}
        />
        <Stack.Screen
          name="(auth)"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="(classes)"
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen name="+not-found" />
      </Stack>
      <PortalHost />
    </ThemeProvider>
  );
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: 1000 * 60 * 60 * 1, // 1 hour
      staleTime: 2000,
      refetchInterval: 30 * 1000,
      refetchIntervalInBackground: true,
      networkMode: "offlineFirst",
      refetchOnWindowFocus: true,
    },
  },
});

const RootLayoutNav = () => {
  return (
    <NotificationProvider>
      <NotificationManager>
        <FontScaleProvider disableFontScaling={true} maxFontScale={1.0}>
          <PersistQueryClientProvider
            client={queryClient}
            persistOptions={{
              persister: clientPersister,
              maxAge: Infinity,
            }}
          >
            <RootSiblingParent>
              <GestureHandlerRootView style={{ flex: 1 }}>
                <KeyboardProvider>
                  <AuthContextProvider>
                    <PermissionProvider>
                      <StatusBar style="light" />
                      <InitialLayout />
                    </PermissionProvider>
                  </AuthContextProvider>
                </KeyboardProvider>
              </GestureHandlerRootView>
            </RootSiblingParent>
          </PersistQueryClientProvider>
        </FontScaleProvider>
      </NotificationManager>
    </NotificationProvider>
  );
};

export default RootLayoutNav;

const useIsomorphicLayoutEffect =
  Platform.OS === "web" && typeof window === "undefined"
    ? React.useEffect
    : React.useLayoutEffect;

function useSetWebBackgroundClassName() {
  useIsomorphicLayoutEffect(() => {
    // Adds the background color to the html element to prevent white background on overscroll.
    document.documentElement.classList.add("bg-background");
  }, []);
}

function useSetAndroidNavigationBar() {
  React.useLayoutEffect(() => {
    setAndroidNavigationBar(Appearance.getColorScheme() ?? "light");
  }, []);
}

function noop() {}
