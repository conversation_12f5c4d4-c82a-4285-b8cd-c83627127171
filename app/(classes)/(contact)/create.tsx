import React from "react";
import { View } from "react-native";
import { router } from "expo-router";
import * as z from "zod";

import { ScreenTracker } from "~/components/analytics/ScreenTracker";

import {
  ContactClassForm,
  ContactClassSchema,
} from "~/components/modules/contact-classes/contact-class-form";
import { useCreateContactClass } from "~/modules/contact-classes/mutations/useCreateContactClass";

function combineDateAndTime(sendDate: string, timeDate: string): string {
  const datePart = sendDate.split("T")[0];
  const timePart = timeDate.split("T")[1];

  return `${datePart}T${timePart}`;
}

export default function ContactClassPage() {
  const { mutate: createContactClass, isPending } = useCreateContactClass(
    () => {
      router.back();
    }
  );

  const handleSubmit = async (formData: z.infer<typeof ContactClassSchema>) => {
    const classes = formData.classes.map((cls) => cls.id);

    createContactClass({
      date: formData.date,
      reason: formData.reason,
      classes,
      send_at: combineDateAndTime(
        formData.send_at_date.toISOString(),
        formData.time.toISOString()
      ),
    });
  };

  return (
    <ScreenTracker screenName="Create Cancelled Class">
      <View className="flex-1 bg-background">
        <ContactClassForm isLoading={isPending} onSubmit={handleSubmit} />
      </View>
    </ScreenTracker>
  );
}
