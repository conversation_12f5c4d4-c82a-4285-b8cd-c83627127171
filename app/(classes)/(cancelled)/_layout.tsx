import { router, Stack, useLocalSearchParams } from "expo-router";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useColorScheme } from "~/lib/useColorScheme";

export default function CancelledClassesLayout() {
  const { isDarkColorScheme } = useColorScheme();

  const { id } = useLocalSearchParams<{
    id: string;
  }>();

  return (
    <Stack>
      <Stack.Screen
        name="create"
        options={{
          title: id ? "UPDATE CANCELLED CLASS" : "CREATE CANCELLED CLASS",
          headerTitleStyle: {
            color: isDarkColorScheme ? "white" : "#002966",
          },
          headerLeft: () => (
            <MaterialIcons
              name="arrow-back"
              size={20}
              color={isDarkColorScheme ? "white" : "#002966"}
              onPress={router.back}
            />
          ),
        }}
      />
    </Stack>
  );
}
