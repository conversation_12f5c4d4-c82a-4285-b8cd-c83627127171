import FontAwesome from "@expo/vector-icons/FontAwesome";
import { Tabs } from "expo-router";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import FontAwesome6 from "@expo/vector-icons/FontAwesome6";

import { useColorScheme } from "~/lib/useColorScheme";
import { FEATURES, useFeatureEnabled } from "~/modules/hooks/useFeatureEnabled";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { isUpaceEmail } from "~/lib/utils";
import { useSession } from "~/modules/login/auth-provider";

export default function TabLayout() {
  const { isDarkColorScheme } = useColorScheme();
  const isFeatureEnabled = useFeatureEnabled(FEATURES.SUB_MANAGER_MODULE);
  const { trackEvent, EVENTS } = useAnalytics();

  const { data: sessionData } = useSession();

  const handleTabPress = (tabName: string) =>
    trackEvent(EVENTS.TAB_CHANGE, { tab_name: tabName });

  return (
    <Tabs
      initialRouteName="index"
      screenOptions={{
        tabBarActiveTintColor: "blue",
        tabBarAccessibilityLabel: "Tab Navigation",
        tabBarAllowFontScaling: false,
        tabBarLabelStyle: { fontSize: 12 },
      }}
      screenListeners={{
        tabPress: (e) => {
          const tabName = e.target?.split("-")[0] || "unknown";
          handleTabPress(tabName);
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Home",
          headerShown: false,
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          tabBarAccessibilityLabel: "Home tab",
          tabBarIcon: ({ color, size }) => (
            <FontAwesome
              size={size}
              name="home"
              color={isDarkColorScheme ? "white" : color}
              accessibilityLabel="Home icon"
            />
          ),
        }}
        listeners={{
          focus: () => handleTabPress("Home"),
        }}
      />
      <Tabs.Screen
        name="classes"
        options={{
          title: "Classes",
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          headerShown: false,
          tabBarAccessibilityLabel: "Classes tab",
          tabBarIcon: ({ color }) => (
            <FontAwesome6
              name="users"
              size={20}
              color={isDarkColorScheme ? "white" : color}
              accessibilityLabel="Classes icon"
            />
          ),
        }}
      />

      <Tabs.Screen
        name="subs"
        options={{
          href: isFeatureEnabled ? "/(classes)/subs" : null,
          title: "Sub Mgt",
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          headerShown: false,
          tabBarAccessibilityLabel: "Substitute Management tab",
          tabBarIcon: ({ color }) => (
            <MaterialIcons
              name="swap-calls"
              size={27}
              color={isDarkColorScheme ? "white" : color}
              accessibilityLabel="Substitute Management icon"
            />
          ),
        }}
      />

      <Tabs.Screen
        name="appointments"
        options={{
          title: "Appts",
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          headerShown: false,
          tabBarAccessibilityLabel: "Appointments tab",
          tabBarIcon: ({ color }) => (
            <FontAwesome
              name="calendar"
              size={24}
              color={isDarkColorScheme ? "white" : color}
              accessibilityLabel="Appointments icon"
            />
          ),
        }}
      />

      <Tabs.Screen
        name="cancelled"
        options={{
          href: null, // Hide from tab bar
          headerShown: false,
        }}
      />
      <Tabs.Screen
        name="contact"
        options={{
          href: null, // Hide from tab bar
          headerShown: false,
        }}
      />

      <Tabs.Screen
        name="settings"
        options={{
          href: isUpaceEmail(sessionData?.email) ? "/settings" : null,
        }}
      />

      {/* <Tabs.Screen
        name="swipecards"
        options={{
          title: "Swipe",
          tabBarActiveTintColor: isDarkColorScheme ? "white" : "black",
          headerShown: false, // Hide the header completely
          headerTransparent: true, // Make header transparent
          tabBarIcon: ({ color }) => (
            <Ionicons
              name="card"
              size={24}
              color={isDarkColorScheme ? "white" : color}
            />
          ),
        }}
      /> */}
    </Tabs>
  );
}
