import React, { useState, useMemo } from "react";
import { View, RefreshControl } from "react-native";
import { FlashList } from "@shopify/flash-list";
import { router } from "expo-router";
import { Text } from "~/components/ui/text";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { CancelledClassCard } from "~/components/modules/cancelled-classes/cancelled-class-card";
import { useCancelledClassesQuery } from "~/modules/cancelled-classes/queries/useCancelledClassesQuery";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { matchSorter } from "match-sorter";
import { format, parseISO, sub } from "date-fns";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { IconDatePicker } from "~/components/modules/common/icon-date-picker";
import { PERMISSIONS, withPermission } from "~/modules/permissions";

export function CancelledClassesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const { trackEvent, EVENTS } = useAnalytics();

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  const {
    data: cancelledClasses = [],
    isPending,
    refetch,
  } = useCancelledClassesQuery(selectedDate);

  // Filter cancelled classes based on search term
  const filteredClasses = useMemo(() => {
    if (!searchTerm.trim()) return cancelledClasses;

    return matchSorter(cancelledClasses, searchTerm, {
      keys: [
        "reason",
        "classes",
        (item) => format(parseISO(item.date), "EEE MMM d, yyyy"),
      ],
    });
  }, [cancelledClasses, searchTerm]);

  const handleCreateCancelledClass = () => {
    trackEvent(EVENTS.CLASS_ACTION, {
      action: "create_cancelled_class",
      screen: "Cancelled Classes",
    });
    router.push("/(cancelled)/create");
  };

  const renderEmptyState = () => (
    <View className="flex-1 justify-center items-center px-6 py-12">
      <MaterialIcons
        name="cancel"
        size={64}
        color="#9CA3AF"
        style={{ marginBottom: 16 }}
      />
      <Text className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2 text-center">
        No Cancelled Classes
      </Text>
      <Text className="text-gray-500 dark:text-gray-400 text-center mb-6">
        {searchTerm
          ? "No cancelled classes match your search."
          : "You haven't cancelled any classes yet."}
      </Text>
      {!searchTerm && (
        <Button
          onPress={handleCreateCancelledClass}
          className="px-6"
          label="Cancel Classes"
        />
      )}
    </View>
  );

  const renderSkeleton = () => (
    <View className="px-4 py-2">
      {Array.from({ length: 5 }).map((_, index) => (
        <View
          key={index}
          className="mb-3 p-4 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"
        >
          <View className="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2 w-3/4" />
          <View className="h-3 bg-gray-300 dark:bg-gray-600 rounded mb-1 w-full" />
          <View className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2" />
        </View>
      ))}
    </View>
  );

  return (
    <ScreenTracker screenName="Cancelled Classes">
      <View className="flex-1 bg-background">
        <View className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <View className="flex flex-row justify-between mb-4">
            <Text className="text-lg mt-2 font-extrabold text-[#002966] dark:text-white">
              CANCELLED CLASSES
            </Text>
            <Button
              onPress={() => router.push("/(cancelled)/create")}
              className="bg-[#002966]"
              label="Cancel Class"
            />
          </View>

          <View className="flex flex-row justify-between items-center">
            <Input
              placeholder="Search cancelled classes..."
              value={searchTerm}
              onChangeText={setSearchTerm}
              className="bg-white dark:bg-gray-800 w-[90%]"
            />
            <IconDatePicker
              onChange={setSelectedDate}
              value={selectedDate}
              minDate={sub(new Date(), { months: 6 })}
            />
          </View>
        </View>

        <View className="flex-1">
          {isPending ? (
            renderSkeleton()
          ) : filteredClasses.length === 0 ? (
            renderEmptyState()
          ) : (
            <FlashList
              data={filteredClasses}
              renderItem={({ item }) => (
                <View className="px-4">
                  <CancelledClassCard {...item} />
                </View>
              )}
              keyExtractor={(item) => item.id.toString()}
              estimatedItemSize={800}
              refreshControl={
                <RefreshControl refreshing={isPending} onRefresh={refetch} />
              }
              contentContainerStyle={{
                paddingTop: 16,
                paddingBottom: 32,
              }}
            />
          )}
        </View>
      </View>
    </ScreenTracker>
  );
}

export default withPermission(
  CancelledClassesPage,
  PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS
);
