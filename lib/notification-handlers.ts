import { Platform } from "react-native";
import * as Notifications from "expo-notifications";
import { router } from "expo-router";
import { logEvent } from "./firebase";

// Types for notification handling
export interface NotificationAction {
  type: "navigate" | "deep_link" | "custom";
  payload: any;
}

export interface NotificationHandler {
  id: string;
  condition: (data: any) => boolean;
  action: NotificationAction;
}

class NotificationHandlerService {
  private handlers: NotificationHandler[] = [];
  private isSetup = false;

  /**
   * Set up notification handlers
   */
  setup(): void {
    if (this.isSetup) return;

    this.setupDefaultHandlers();
    this.isSetup = true;
  }

  /**
   * Register a custom notification handler
   */
  registerHandler(handler: NotificationHandler): void {
    this.handlers.push(handler);
  }

  /**
   * Remove a notification handler
   */
  removeHandler(id: string): void {
    this.handlers = this.handlers.filter((h) => h.id !== id);
  }

  /**
   * Process notification data and execute appropriate handler
   */
  async processNotification(
    data: any,
    source: "foreground" | "background" | "opened"
  ): Promise<void> {
    try {
      // Log notification event
      await logEvent("notification_received", {
        source,
        type: data?.type || "unknown",
        has_custom_data: Object.keys(data || {}).length > 0,
      });

      // Find matching handler
      const handler = this.handlers.find((h) => h.condition(data));

      if (handler) {
        await this.executeHandler(handler, data, source);
      } else {
        // Default handling
        await this.handleDefault(data, source);
      }
    } catch (error) {
      console.error("Error processing notification:", error);
      await logEvent("notification_error", {
        source,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  /**
   * Execute a notification handler
   */
  private async executeHandler(
    handler: NotificationHandler,
    data: any,
    source: string
  ): Promise<void> {
    const { action } = handler;

    switch (action.type) {
      case "navigate":
        await this.handleNavigation(action.payload, data);
        break;
      case "deep_link":
        await this.handleDeepLink(action.payload, data);
        break;
      case "custom":
        if (typeof action.payload === "function") {
          await action.payload(data, source);
        }
        break;
      default:
        console.warn("Unknown notification action type:", action.type);
    }

    // Log handler execution
    await logEvent("notification_handler_executed", {
      handler_id: handler.id,
      action_type: action.type,
      source,
    });
  }

  /**
   * Handle navigation action
   */
  private async handleNavigation(payload: any, data: any): Promise<void> {
    const { screen, params } = payload;

    if (screen) {
      try {
        // Add notification data to params
        const navigationParams = {
          ...params,
          notificationData: data,
        };

        router.push({
          pathname: screen,
          params: navigationParams,
        });

        await logEvent("notification_navigation", {
          screen,
          has_params: Object.keys(params || {}).length > 0,
        });
      } catch (error) {
        console.error("Navigation error:", error);
      }
    }
  }

  /**
   * Handle deep link action
   */
  private async handleDeepLink(payload: any, data: any): Promise<void> {
    const { url } = payload;

    if (url) {
      try {
        // You can implement deep link handling here
        console.log("Deep link:", url, data);

        await logEvent("notification_deep_link", {
          url,
        });
      } catch (error) {
        console.error("Deep link error:", error);
      }
    }
  }

  /**
   * Default notification handling
   */
  private async handleDefault(data: any, source: string): Promise<void> {
    console.log("Default notification handling:", { data, source });

    // Default behavior based on source
    if (source === "opened") {
      // App was opened from notification - could navigate to a default screen
      // router.push('/notifications');
    }
  }

  /**
   * Set up default notification handlers
   */
  private setupDefaultHandlers(): void {
    // Class/appointment related notifications
    this.registerHandler({
      id: "class_reminder",
      condition: (data) => data?.type === "class_reminder",
      action: {
        type: "navigate",
        payload: {
          screen: "/(classes)/[id]",
          params: { id: "{{class_id}}" }, // Will be replaced with actual data
        },
      },
    });

    // New member notifications
    this.registerHandler({
      id: "new_member",
      condition: (data) => data?.type === "new_member",
      action: {
        type: "navigate",
        payload: {
          screen: "/(members)",
        },
      },
    });

    // Birthday notifications
    this.registerHandler({
      id: "birthday",
      condition: (data) => data?.type === "birthday",
      action: {
        type: "navigate",
        payload: {
          screen: "/(classes)/[id]/notifications",
          params: { id: "{{class_id}}" },
        },
      },
    });

    // General announcements
    this.registerHandler({
      id: "announcement",
      condition: (data) => data?.type === "announcement",
      action: {
        type: "navigate",
        payload: {
          screen: "/announcements",
        },
      },
    });

    // Schedule changes
    this.registerHandler({
      id: "schedule_change",
      condition: (data) => data?.type === "schedule_change",
      action: {
        type: "navigate",
        payload: {
          screen: "/(tabs)",
        },
      },
    });
  }

  /**
   * Create local notification
   */
  async createLocalNotification(
    title: string,
    body: string,
    data?: any,
    options?: {
      sound?: string;
      badge?: number;
      categoryId?: string;
      trigger?: Notifications.NotificationTriggerInput;
    }
  ): Promise<string> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
          sound: options?.sound || "default",
          badge: options?.badge,
          categoryIdentifier: options?.categoryId,
        },
        trigger: options?.trigger || null, // null means show immediately
      });

      await logEvent("local_notification_created", {
        title: title.substring(0, 50), // Truncate for privacy
        has_data: Object.keys(data || {}).length > 0,
      });

      return notificationId;
    } catch (error) {
      console.error("Error creating local notification:", error);
      throw error;
    }
  }

  /**
   * Cancel a local notification
   */
  async cancelLocalNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);

      await logEvent("local_notification_cancelled", {
        notification_id: notificationId,
      });
    } catch (error) {
      console.error("Error cancelling local notification:", error);
      throw error;
    }
  }

  /**
   * Cancel all local notifications
   */
  async cancelAllLocalNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();

      await logEvent("all_local_notifications_cancelled");
    } catch (error) {
      console.error("Error cancelling all local notifications:", error);
      throw error;
    }
  }

  /**
   * Get pending notifications
   */
  async getPendingNotifications(): Promise<
    Notifications.NotificationRequest[]
  > {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error("Error getting pending notifications:", error);
      return [];
    }
  }

  /**
   * Set notification badge count
   */
  async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);

      await logEvent("badge_count_set", { count });
    } catch (error) {
      console.error("Error setting badge count:", error);
    }
  }

  /**
   * Get notification badge count
   */
  async getBadgeCount(): Promise<number> {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error("Error getting badge count:", error);
      return 0;
    }
  }

  /**
   * Clear all notifications from notification center
   */
  async clearAllNotifications(): Promise<void> {
    try {
      await Notifications.dismissAllNotificationsAsync();

      await logEvent("all_notifications_cleared");
    } catch (error) {
      console.error("Error clearing all notifications:", error);
    }
  }

  /**
   * Get notification settings/permissions
   */
  async getNotificationSettings(): Promise<Notifications.NotificationPermissionsStatus> {
    try {
      return await Notifications.getPermissionsAsync();
    } catch (error) {
      console.error("Error getting notification settings:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const notificationHandlerService = new NotificationHandlerService();
export default notificationHandlerService;
