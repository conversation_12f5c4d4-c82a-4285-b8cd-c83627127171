import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { logEvent } from './firebase';
import { getSession } from '~/modules/login/auth-provider';

// Types
export interface TopicSubscription {
  topic: string;
  subscribed: boolean;
  subscribedAt?: string;
  unsubscribedAt?: string;
}

export interface TopicConfig {
  id: string;
  name: string;
  description: string;
  defaultEnabled: boolean;
  category: 'classes' | 'members' | 'general' | 'admin';
  icon: string;
  priority: 'low' | 'normal' | 'high';
}

// Storage keys
const STORAGE_KEYS = {
  SUBSCRIPTIONS: '@notification_subscriptions',
  LAST_SYNC: '@notification_last_sync',
} as const;

// Default topic configurations
const DEFAULT_TOPICS: TopicConfig[] = [
  {
    id: 'class_reminders',
    name: 'Class Reminders',
    description: 'Get notified about upcoming classes',
    defaultEnabled: true,
    category: 'classes',
    icon: 'time',
    priority: 'high',
  },
  {
    id: 'schedule_changes',
    name: 'Schedule Changes',
    description: 'Updates about class schedule modifications',
    defaultEnabled: true,
    category: 'classes',
    icon: 'calendar',
    priority: 'high',
  },
  {
    id: 'new_members',
    name: 'New Members',
    description: 'Notifications when new members join',
    defaultEnabled: true,
    category: 'members',
    icon: 'person-add',
    priority: 'normal',
  },
  {
    id: 'member_birthdays',
    name: 'Member Birthdays',
    description: 'Birthday reminders for your members',
    defaultEnabled: false,
    category: 'members',
    icon: 'gift',
    priority: 'low',
  },
  {
    id: 'sub_requests',
    name: 'Sub Requests',
    description: 'Notifications about substitute requests',
    defaultEnabled: true,
    category: 'classes',
    icon: 'swap-horizontal',
    priority: 'high',
  },
  {
    id: 'announcements',
    name: 'Announcements',
    description: 'Important updates and announcements',
    defaultEnabled: true,
    category: 'general',
    icon: 'megaphone',
    priority: 'normal',
  },
  {
    id: 'system_updates',
    name: 'System Updates',
    description: 'App updates and maintenance notifications',
    defaultEnabled: false,
    category: 'admin',
    icon: 'settings',
    priority: 'low',
  },
  {
    id: 'payment_reminders',
    name: 'Payment Reminders',
    description: 'Reminders about member payments',
    defaultEnabled: true,
    category: 'members',
    icon: 'card',
    priority: 'normal',
  },
];

class TopicSubscriptionManager {
  private subscriptions: Map<string, TopicSubscription> = new Map();
  private isInitialized = false;

  /**
   * Initialize the topic subscription manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSubscriptions();
      await this.syncWithDefaults();
      this.isInitialized = true;
      
      console.log('Topic subscription manager initialized');
      await logEvent('topic_manager_initialized', {
        total_topics: this.subscriptions.size,
        subscribed_count: Array.from(this.subscriptions.values()).filter(s => s.subscribed).length,
      });
    } catch (error) {
      console.error('Failed to initialize topic subscription manager:', error);
      throw error;
    }
  }

  /**
   * Load subscriptions from storage
   */
  private async loadSubscriptions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.SUBSCRIPTIONS);
      if (stored) {
        const subscriptions: TopicSubscription[] = JSON.parse(stored);
        this.subscriptions = new Map(
          subscriptions.map(sub => [sub.topic, sub])
        );
      }
    } catch (error) {
      console.error('Error loading subscriptions:', error);
    }
  }

  /**
   * Save subscriptions to storage
   */
  private async saveSubscriptions(): Promise<void> {
    try {
      const subscriptions = Array.from(this.subscriptions.values());
      await AsyncStorage.setItem(
        STORAGE_KEYS.SUBSCRIPTIONS,
        JSON.stringify(subscriptions)
      );
      await AsyncStorage.setItem(
        STORAGE_KEYS.LAST_SYNC,
        new Date().toISOString()
      );
    } catch (error) {
      console.error('Error saving subscriptions:', error);
    }
  }

  /**
   * Sync with default topics
   */
  private async syncWithDefaults(): Promise<void> {
    let hasChanges = false;

    for (const topicConfig of DEFAULT_TOPICS) {
      if (!this.subscriptions.has(topicConfig.id)) {
        // New topic - subscribe if default enabled
        const subscription: TopicSubscription = {
          topic: topicConfig.id,
          subscribed: topicConfig.defaultEnabled,
          subscribedAt: topicConfig.defaultEnabled ? new Date().toISOString() : undefined,
        };

        this.subscriptions.set(topicConfig.id, subscription);

        // Subscribe to Firebase topic if enabled
        if (topicConfig.defaultEnabled) {
          try {
            await messaging().subscribeToTopic(topicConfig.id);
            console.log(`Auto-subscribed to topic: ${topicConfig.id}`);
          } catch (error) {
            console.error(`Failed to auto-subscribe to topic ${topicConfig.id}:`, error);
          }
        }

        hasChanges = true;
      }
    }

    if (hasChanges) {
      await this.saveSubscriptions();
    }
  }

  /**
   * Subscribe to a topic
   */
  async subscribeToTopic(topicId: string): Promise<void> {
    try {
      // Subscribe to Firebase topic
      await messaging().subscribeToTopic(topicId);

      // Update local subscription
      const subscription: TopicSubscription = {
        topic: topicId,
        subscribed: true,
        subscribedAt: new Date().toISOString(),
      };

      this.subscriptions.set(topicId, subscription);
      await this.saveSubscriptions();

      console.log(`Subscribed to topic: ${topicId}`);
      await logEvent('topic_subscribed', { topic: topicId });
    } catch (error) {
      console.error(`Error subscribing to topic ${topicId}:`, error);
      throw error;
    }
  }

  /**
   * Unsubscribe from a topic
   */
  async unsubscribeFromTopic(topicId: string): Promise<void> {
    try {
      // Unsubscribe from Firebase topic
      await messaging().unsubscribeFromTopic(topicId);

      // Update local subscription
      const existing = this.subscriptions.get(topicId);
      const subscription: TopicSubscription = {
        topic: topicId,
        subscribed: false,
        subscribedAt: existing?.subscribedAt,
        unsubscribedAt: new Date().toISOString(),
      };

      this.subscriptions.set(topicId, subscription);
      await this.saveSubscriptions();

      console.log(`Unsubscribed from topic: ${topicId}`);
      await logEvent('topic_unsubscribed', { topic: topicId });
    } catch (error) {
      console.error(`Error unsubscribing from topic ${topicId}:`, error);
      throw error;
    }
  }

  /**
   * Toggle topic subscription
   */
  async toggleTopicSubscription(topicId: string): Promise<boolean> {
    const subscription = this.subscriptions.get(topicId);
    const isCurrentlySubscribed = subscription?.subscribed ?? false;

    if (isCurrentlySubscribed) {
      await this.unsubscribeFromTopic(topicId);
      return false;
    } else {
      await this.subscribeToTopic(topicId);
      return true;
    }
  }

  /**
   * Get all topic configurations
   */
  getTopicConfigs(): TopicConfig[] {
    return DEFAULT_TOPICS;
  }

  /**
   * Get topic configuration by ID
   */
  getTopicConfig(topicId: string): TopicConfig | undefined {
    return DEFAULT_TOPICS.find(topic => topic.id === topicId);
  }

  /**
   * Get subscription status for a topic
   */
  isSubscribedToTopic(topicId: string): boolean {
    return this.subscriptions.get(topicId)?.subscribed ?? false;
  }

  /**
   * Get all subscriptions
   */
  getAllSubscriptions(): TopicSubscription[] {
    return Array.from(this.subscriptions.values());
  }

  /**
   * Get subscribed topics only
   */
  getSubscribedTopics(): string[] {
    return Array.from(this.subscriptions.values())
      .filter(sub => sub.subscribed)
      .map(sub => sub.topic);
  }

  /**
   * Get topics by category
   */
  getTopicsByCategory(category: TopicConfig['category']): TopicConfig[] {
    return DEFAULT_TOPICS.filter(topic => topic.category === category);
  }

  /**
   * Subscribe to user-specific topics based on session
   */
  async subscribeToUserTopics(): Promise<void> {
    try {
      const session = await getSession();
      if (!session?.email) return;

      // Create user-specific topic (e.g., based on email hash or user ID)
      const userTopic = `user_${session.email.replace(/[^a-zA-Z0-9]/g, '_')}`;
      
      // Subscribe to user-specific notifications
      await this.subscribeToTopic(userTopic);

      // Subscribe to role-based topics if applicable
      // You can add logic here based on user roles or permissions

      await logEvent('user_topics_subscribed', {
        user_topic: userTopic,
        email_domain: session.email.split('@')[1],
      });
    } catch (error) {
      console.error('Error subscribing to user topics:', error);
    }
  }

  /**
   * Bulk subscribe/unsubscribe operations
   */
  async bulkUpdateSubscriptions(updates: { topicId: string; subscribe: boolean }[]): Promise<void> {
    const promises = updates.map(update => 
      update.subscribe 
        ? this.subscribeToTopic(update.topicId)
        : this.unsubscribeFromTopic(update.topicId)
    );

    try {
      await Promise.all(promises);
      await logEvent('bulk_subscription_update', {
        total_updates: updates.length,
        subscriptions: updates.filter(u => u.subscribe).length,
        unsubscriptions: updates.filter(u => !u.subscribe).length,
      });
    } catch (error) {
      console.error('Error in bulk subscription update:', error);
      throw error;
    }
  }

  /**
   * Reset all subscriptions to defaults
   */
  async resetToDefaults(): Promise<void> {
    try {
      // Unsubscribe from all current topics
      const currentTopics = this.getSubscribedTopics();
      for (const topic of currentTopics) {
        await messaging().unsubscribeFromTopic(topic);
      }

      // Clear local subscriptions
      this.subscriptions.clear();

      // Re-sync with defaults
      await this.syncWithDefaults();

      await logEvent('subscriptions_reset_to_defaults', {
        previous_count: currentTopics.length,
        new_count: this.getSubscribedTopics().length,
      });

      console.log('Subscriptions reset to defaults');
    } catch (error) {
      console.error('Error resetting subscriptions:', error);
      throw error;
    }
  }

  /**
   * Get subscription statistics
   */
  getSubscriptionStats(): {
    total: number;
    subscribed: number;
    byCategory: Record<string, { total: number; subscribed: number }>;
  } {
    const stats = {
      total: DEFAULT_TOPICS.length,
      subscribed: this.getSubscribedTopics().length,
      byCategory: {} as Record<string, { total: number; subscribed: number }>,
    };

    // Calculate by category
    for (const topic of DEFAULT_TOPICS) {
      if (!stats.byCategory[topic.category]) {
        stats.byCategory[topic.category] = { total: 0, subscribed: 0 };
      }
      stats.byCategory[topic.category].total++;
      if (this.isSubscribedToTopic(topic.id)) {
        stats.byCategory[topic.category].subscribed++;
      }
    }

    return stats;
  }

  /**
   * Check if manager is initialized
   */
  isManagerInitialized(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const topicSubscriptionManager = new TopicSubscriptionManager();
export default topicSubscriptionManager;
