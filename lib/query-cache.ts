import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister";
import { MMKV } from "react-native-mmkv";

let storage;
try {
  storage = new MMKV();
} catch (e) {
  console.warn("MMKV initialization failed, falling back to in-memory storage:", e);
  // Fallback to a simple in-memory object for development/debugging
  const inMemoryStorage: Record<string, string> = {};
  storage = {
    set: (key: string, value: string) => {
      inMemoryStorage[key] = value;
    },
    getString: (key: string) => {
      return inMemoryStorage[key] || undefined;
    },
    delete: (key: string) => {
      delete inMemoryStorage[key];
    },
  };
}

const clientStorage = {
  setItem: (key: string, value: string) => {
    storage.set(key, value);
  },
  getItem: (key: string) => {
    const value = storage.getString(key);
    return value === undefined ? null : value;
  },
  removeItem: (key: string) => {
    storage.delete(key);
  },
};

export const clientPersister = createSyncStoragePersister({
  storage: clientStorage,
});
