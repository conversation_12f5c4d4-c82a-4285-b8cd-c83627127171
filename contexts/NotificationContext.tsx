import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import * as Notifications from "expo-notifications";
import { registerForPushNotificationsAsync } from "~/lib/registerForPushNotificationsAsync";
import { Platform } from "react-native";

interface NotificationContextType {
  expoPushToken: string | null;
  notification: Notifications.Notification | undefined;
  notificationResponse: Notifications.NotificationResponse | undefined;
  error: Error | null;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [error, setError] = useState<Error | null>(null);

  const [expoPushToken, setExpoPushToken] = useState("");
  const [_, setChannels] = useState<Notifications.NotificationChannel[]>([]);

  const [notification, setNotification] = useState<
    Notifications.Notification | undefined
  >(undefined);

  const [notificationResponse, setNotificationResponse] = useState<
    Notifications.NotificationResponse | undefined
  >(undefined);

  useEffect(() => {
    registerForPushNotificationsAsync()
      .then((token) => token && setExpoPushToken(token))
      .catch((error) => setError(error));

    if (Platform.OS === "android") {
      Notifications.getNotificationChannelsAsync().then((value) =>
        setChannels(value ?? [])
      );
    }
    const notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        setNotification(notification);
      }
    );

    const responseListener =
      Notifications.addNotificationResponseReceivedListener((response) => {
        setNotificationResponse(response);
        console.log("Notification:", JSON.stringify(response));
      });

    return () => {
      notificationListener.remove();
      responseListener.remove();
    };
  }, []);

  return (
    <NotificationContext.Provider
      value={{ expoPushToken, notification, notificationResponse, error }}
    >
      {children}
    </NotificationContext.Provider>
  );
};
