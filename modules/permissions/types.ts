export type Permission = string;

// Predefined permission constants for type safety
export const PERMISSIONS = {
  // Classes permissions
  CLASSES: {
    CANCEL_CLASS: {
      ACCESS: "classes.cancel_class.access",
      CANCEL: "classes.cancel_class.cancel",
      UPDATE: "classes.cancel_class.update",
      DELETE: "classes.cancel_class.delete",
    },
    CONTACT_CLASS: {
      ACCESS: "classes.contact_class.access",
      CREATE: "classes.contact_class.create",
      DELETE: "classes.contact_class.delete",
    },
  },
  // Future permissions can be added here
  APPOINTMENTS: {
    CREATE: "appointments.create",
    UPDATE: "appointments.update",
    DELETE: "appointments.delete",
  },
  MEMBERS: {
    VIEW: "members.view",
    CREATE: "members.create",
    UPDATE: "members.update",
  },
} as const;

// Permission check options
export interface PermissionCheckOptions {
  requireAll?: boolean; // If true, all permissions must be granted (AND logic)
  fallback?: boolean; // Default value when permission is not found
}

// Permission context type
export interface PermissionContextType {
  permissions: Permission | Permission[];
  isLoading: boolean;
  error: Error | null;
  hasPermission: (
    permission: Permission | Permission[],
    options?: PermissionCheckOptions
  ) => boolean;
  refetch: () => void;
}
