# Permission System Documentation

A comprehensive, extensible permission system for feature flags and access control in the Upace Instructor app.

## Overview

This permission system provides a declarative way to hide/show UI elements and control access to features based on user permissions from the API. It's designed to be simple, extensible, and type-safe.

## Features

- 🔒 **Declarative Permission Gates** - Wrap components to show/hide based on permissions
- 🎯 **Type-Safe Permissions** - Predefined permission constants with TypeScript support
- 🚀 **Easy Integration** - Simple hooks and components for quick implementation
- 📱 **Accessibility Support** - Built-in accessibility features for permission-controlled elements
- 🔄 **Caching & Performance** - Efficient API caching with React Query
- 🎨 **Flexible Components** - Multiple ways to handle permission-based UI

## Quick Start

### 1. Setup

The `PermissionProvider` is already configured in `app/_layout.tsx`. No additional setup required.

### 2. Basic Usage

```tsx
import { PermissionGate, PERMISSIONS } from "~/modules/permissions";

// Hide entire component when no permission
<PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}>
  <CancelButton />
</PermissionGate>

// Show fallback content when no permission
<PermissionGate 
  permission={PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE}
  fallback={<Text>Access denied</Text>}
>
  <CreateContactButton />
</PermissionGate>
```

### 3. Using Permission Hooks

```tsx
import { usePermission, PERMISSIONS } from "~/modules/permissions";

function MyComponent() {
  const canCancel = usePermission(PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL);
  const canDelete = usePermission(PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE);
  
  return (
    <View>
      {canCancel && <Button onPress={handleCancel} label="Cancel" />}
      {canDelete && <Button onPress={handleDelete} label="Delete" />}
    </View>
  );
}
```

## Available Components

### PermissionGate

Conditionally renders children based on permissions.

```tsx
<PermissionGate 
  permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}
  fallback={<Text>No access</Text>}
>
  <MyComponent />
</PermissionGate>
```

### PermissionButton

Button that automatically handles permission-based enabling/disabling.

```tsx
<PermissionButton
  permission={PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE}
  label="Create Contact"
  onPress={handleCreate}
  hideWhenNoPermission // Optional: hide completely vs show disabled
/>
```

### withPermission HOC

Higher-order component for wrapping entire components.

```tsx
const ProtectedComponent = withPermission(
  MyComponent,
  PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
  { fallback: false }, // Options
  <Text>Access denied</Text> // Fallback
);
```

## Permission Logic

### Single Permission
```tsx
// User must have this exact permission
<PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}>
  <Component />
</PermissionGate>
```

### Multiple Permissions (OR Logic - Default)
```tsx
// User needs ANY of these permissions
<PermissionGate permission={[
  PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
  PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS
]}>
  <Component />
</PermissionGate>
```

### Multiple Permissions (AND Logic)
```tsx
// User needs ALL of these permissions
<PermissionGate 
  permission={[
    PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
    PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE
  ]}
  options={{ requireAll: true }}
>
  <Component />
</PermissionGate>
```

## Available Permissions

### Classes
- `PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS` - Access to cancel class features
- `PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL` - Ability to cancel classes
- `PERMISSIONS.CLASSES.CANCEL_CLASS.UPDATE` - Ability to update cancelled classes
- `PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE` - Ability to delete cancelled classes
- `PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS` - Access to contact class features
- `PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE` - Ability to create contact classes
- `PERMISSIONS.CLASSES.CONTACT_CLASS.DELETE` - Ability to delete contact classes

### Appointments
- `PERMISSIONS.APPOINTMENTS.CREATE` - Create appointments
- `PERMISSIONS.APPOINTMENTS.UPDATE` - Update appointments
- `PERMISSIONS.APPOINTMENTS.DELETE` - Delete appointments

### Members
- `PERMISSIONS.MEMBERS.VIEW` - View member information
- `PERMISSIONS.MEMBERS.CREATE` - Create new members
- `PERMISSIONS.MEMBERS.UPDATE` - Update member information

## Adding New Permissions

1. **Add to types.ts**:
```tsx
export const PERMISSIONS = {
  // ... existing permissions
  NEW_FEATURE: {
    ACCESS: 'new_feature.access',
    CREATE: 'new_feature.create',
    UPDATE: 'new_feature.update',
    DELETE: 'new_feature.delete',
  },
} as const;
```

2. **Update the PermissionString type**:
```tsx
export type PermissionString = 
  | typeof PERMISSIONS.CLASSES.CANCEL_CLASS[keyof typeof PERMISSIONS.CLASSES.CANCEL_CLASS]
  | typeof PERMISSIONS.NEW_FEATURE[keyof typeof PERMISSIONS.NEW_FEATURE] // Add this line
  // ... other types
```

3. **Use in components**:
```tsx
<PermissionGate permission={PERMISSIONS.NEW_FEATURE.ACCESS}>
  <NewFeatureComponent />
</PermissionGate>
```

## API Integration

The system expects the API to return permissions in this format:

```json
{
  "permissions": [
    {
      "resource": "classes.cancel_class",
      "action": "access",
      "granted": true
    },
    {
      "resource": "classes.cancel_class",
      "action": "cancel",
      "granted": false
    }
  ]
}
```

The permission string format is: `{resource}.{action}` (e.g., `classes.cancel_class.access`)

## Performance Considerations

- Permissions are cached for 5 minutes using React Query
- The system gracefully handles API failures by defaulting to no permissions
- Permission checks are memoized for optimal performance
- Use `hideWhenNoPermission` on buttons to reduce DOM nodes when appropriate

## Accessibility

All permission-controlled components maintain proper accessibility:
- Buttons show appropriate disabled states with accessibility hints
- Screen readers announce permission-related state changes
- Proper ARIA labels are maintained for all interactive elements

## Examples in the Codebase

### Navigation (Drawer)
```tsx
// components/modules/classes/drawer-content.tsx
<PermissionGate permission={PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS}>
  <DrawerMenuItem title="Contact Classes" />
</PermissionGate>
```

### Action Buttons
```tsx
// app/(classes)/(tabs)/contact.tsx
<PermissionButton
  permission={PERMISSIONS.CLASSES.CONTACT_CLASS.CREATE}
  label="Contact Class"
  onPress={() => router.push("/(contact)/create")}
  hideWhenNoPermission
/>
```

### Individual Actions
```tsx
// components/modules/appointments/cancel-control.tsx
<PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL}>
  <CancelButton />
</PermissionGate>
```
