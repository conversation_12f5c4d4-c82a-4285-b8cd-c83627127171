import React from "react";
import { But<PERSON> } from "~/components/ui/button";
import { usePermission } from "../PermissionProvider";
import { Permission, PermissionCheckOptions } from "../types";

interface PermissionButtonProps {
  /** Permission(s) required to enable the button */
  permission: Permission;
  /** Options for permission checking */
  options?: PermissionCheckOptions;
  /** Button label */
  label: string;
  /** Button press handler */
  onPress: () => void;
  /** Additional button props */
  buttonProps?: React.ComponentProps<typeof Button>;
  /** Whether to hide button completely when no permission (default: false - shows disabled) */
  hideWhenNoPermission?: boolean;
  /** Custom disabled message */
  disabledMessage?: string;
}

/**
 * Button component that automatically handles permission-based enabling/disabling
 *
 * @example
 * <PermissionButton
 *   permission={PERMISSIONS.CLASSES.CANCEL_CLASS.CANCEL}
 *   label="Cancel Class"
 *   onPress={handleCancel}
 * />
 *
 * @example
 * // Hide button completely when no permission
 * <PermissionButton
 *   permission={PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE}
 *   label="Delete"
 *   onPress={handleDelete}
 *   hideWhenNoPermission
 * />
 */
export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permission,
  options,
  label,
  onPress,
  buttonProps = {},
  hideWhenNoPermission = false,
  disabledMessage,
}) => {
  const hasPermission = usePermission(permission, options);

  // Hide button completely if no permission and hideWhenNoPermission is true
  if (!hasPermission && hideWhenNoPermission) {
    return null;
  }

  return (
    <Button
      {...buttonProps}
      label={label}
      onPress={hasPermission ? onPress : undefined}
      disabled={!hasPermission}
      accessibilityLabel={
        hasPermission
          ? buttonProps.accessibilityLabel || label
          : disabledMessage || `${label} - Access denied`
      }
      accessibilityHint={
        hasPermission
          ? buttonProps.accessibilityHint
          : "You don't have permission to perform this action"
      }
    />
  );
};
