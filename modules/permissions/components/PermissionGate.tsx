import React from "react";
import { usePermission } from "../PermissionProvider";
import { Permission, PermissionCheckOptions } from "../types";

interface PermissionGateProps {
  /** Permission(s) required to show children */
  permission: Permission | Permission[];
  /** Options for permission checking */
  options?: PermissionCheckOptions;
  /** Content to show when permission is granted */
  children: React.ReactNode;
  /** Optional fallback content when permission is denied */
  fallback?: React.ReactNode;
  /** Show loading state while permissions are being fetched */
  showLoadingState?: boolean;
  /** Custom loading component */
  loadingComponent?: React.ReactNode;
}

/**
 * Declarative component to conditionally render content based on permissions
 *
 * @example
 * // Simple usage
 * <PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}>
 *   <CancelButton />
 * </PermissionGate>
 *
 * @example
 * // Multiple permissions (OR logic by default)
 * <PermissionGate permission={[
 *   PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
 *   PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS
 * ]}>
 *   <AdminPanel />
 * </PermissionGate>
 *
 * @example
 * // Multiple permissions with AND logic
 * <PermissionGate
 *   permission={[
 *     PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS,
 *     PERMISSIONS.CLASSES.CANCEL_CLASS.DELETE
 *   ]}
 *   options={{ requireAll: true }}
 * >
 *   <DeleteButton />
 * </PermissionGate>
 *
 * @example
 * // With fallback content
 * <PermissionGate
 *   permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}
 *   fallback={<Text>Access denied</Text>}
 * >
 *   <CancelButton />
 * </PermissionGate>
 */
export const PermissionGate: React.FC<PermissionGateProps> = ({
  permission,
  options,
  children,
  fallback = null,
}) => {
  const hasPermission = usePermission(permission, options);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
};

/**
 * Higher-order component version of PermissionGate
 * Useful for wrapping entire components
 */
export const withPermission = <P extends object>(
  Component: React.ComponentType<P>,
  permission: Permission | Permission[],
  options?: PermissionCheckOptions,
  fallback?: React.ReactNode
) => {
  const WrappedComponent: React.FC<P> = (props) => (
    <PermissionGate
      permission={permission}
      options={options}
      fallback={fallback}
    >
      <Component {...props} />
    </PermissionGate>
  );

  WrappedComponent.displayName = `withPermission(${
    Component.displayName || Component.name
  })`;

  return WrappedComponent;
};
