import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { showSuccessToast } from "~/components/toast";

export const deleteContactClass = async (id: number) => {
  try {
    const response = await api
      .delete<{ message: string; success: boolean }>(
        `classes/contact/${id}/delete`
      )
      .json();
    return response;
  } catch (error) {
    return {
      success: false,
      message: "Unable to delete contact class",
    };
  }
};

export const useDeleteContactClass = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteContactClass,
    onSuccess: async (data) => {
      if (data.success) {
        await queryClient.invalidateQueries({
          queryKey: ["contact-classes"],
        });

        showSuccessToast(data.message);
        onSuccess?.();
      }
    },
  });
};
