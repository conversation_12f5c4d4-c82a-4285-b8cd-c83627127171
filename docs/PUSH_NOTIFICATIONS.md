# Push Notifications Implementation Guide

This document provides comprehensive information about the push notification system implemented in the Upace Connect instructor app.

## Overview

The push notification system combines Firebase Cloud Messaging (FCM) and Expo's push notification service to provide reliable, cross-platform push notifications. The implementation includes:

- **Dual notification services**: Both Firebase and Expo for maximum reliability
- **Topic-based messaging**: Targeted notifications based on user preferences
- **Comprehensive handlers**: Support for foreground, background, and quit states
- **Rich UI components**: In-app notification banners and settings management
- **Testing utilities**: Development tools for testing notifications
- **Analytics integration**: Event tracking for notification performance

## Architecture

### Core Components

1. **PushNotificationService** (`lib/push-notifications.ts`)
   - Main service for notification management
   - Handles permissions, tokens, and message processing
   - Integrates with both Firebase and Expo services

2. **NotificationHandlerService** (`lib/notification-handlers.ts`)
   - Processes incoming notifications
   - Manages notification actions and navigation
   - Handles local notifications

3. **TopicSubscriptionManager** (`lib/topic-subscription-manager.ts`)
   - Manages topic subscriptions
   - Handles user preferences
   - Provides bulk operations

4. **NotificationContext** (`contexts/NotificationContext.tsx`)
   - React context for notification state management
   - Provides hooks for components
   - Manages initialization and permissions

## Installation and Setup

### 1. Dependencies

The following packages are required and have been installed:

```bash
npm install expo-notifications expo-device expo-constants @react-native-firebase/messaging
```

### 2. Configuration

#### app.json Configuration

The app.json has been configured with:

```json
{
  "plugins": [
    "@react-native-firebase/app",
    "@react-native-firebase/messaging",
    [
      "expo-notifications",
      {
        "icon": "./assets/images/appstore.png",
        "color": "#069CC3",
        "sounds": ["./assets/sounds/notification.wav"],
        "defaultChannel": "default"
      }
    ]
  ],
  "android": {
    "permissions": [
      "android.permission.POST_NOTIFICATIONS"
    ]
  },
  "ios": {
    "infoPlist": {
      "UIBackgroundModes": ["remote-notification"]
    },
    "entitlements": {
      "aps-environment": "production"
    }
  }
}
```

#### Firebase Configuration

Firebase messaging is automatically configured in `lib/firebase.ts` during app initialization.

### 3. App Integration

#### Wrap Your App with NotificationProvider

```tsx
import { NotificationProvider } from '~/contexts/NotificationContext';
import { NotificationManager } from '~/components/notifications/NotificationManager';

export default function App() {
  return (
    <NotificationProvider>
      <NotificationManager>
        {/* Your app content */}
      </NotificationManager>
    </NotificationProvider>
  );
}
```

## Usage Examples

### Basic Usage

#### Using the Notification Hook

```tsx
import { useNotifications } from '~/contexts/NotificationContext';

function MyComponent() {
  const {
    hasPermission,
    requestPermissions,
    subscribeToTopic,
    sendTestNotification,
    isLoading,
  } = useNotifications();

  const handleEnableNotifications = async () => {
    if (!hasPermission) {
      const granted = await requestPermissions();
      if (granted) {
        // Subscribe to relevant topics
        await subscribeToTopic('class_reminders');
        await subscribeToTopic('new_members');
      }
    }
  };

  return (
    <View>
      {!hasPermission && (
        <Button
          title="Enable Notifications"
          onPress={handleEnableNotifications}
          disabled={isLoading}
        />
      )}
    </View>
  );
}
```

#### Sending Test Notifications

```tsx
import { notificationTestingService } from '~/lib/notification-testing';

// Send a predefined test notification
const sendTestNotification = async () => {
  const result = await notificationTestingService.sendTestNotification('class_reminder');
  console.log('Test result:', result);
};

// Send a custom test notification
const sendCustomNotification = async () => {
  const result = await notificationTestingService.sendCustomTestNotification(
    'Custom Title',
    'This is a custom test message',
    { custom_data: 'value' },
    'info'
  );
  console.log('Custom test result:', result);
};
```

### Topic Management

#### Subscribe to Topics

```tsx
import { topicSubscriptionManager } from '~/lib/topic-subscription-manager';

// Subscribe to a single topic
await topicSubscriptionManager.subscribeToTopic('class_reminders');

// Bulk subscription updates
await topicSubscriptionManager.bulkUpdateSubscriptions([
  { topicId: 'class_reminders', subscribe: true },
  { topicId: 'member_birthdays', subscribe: false },
  { topicId: 'announcements', subscribe: true },
]);

// Get subscription status
const isSubscribed = topicSubscriptionManager.isSubscribedToTopic('class_reminders');
```

#### Topic Configuration

Available topics are defined in `lib/topic-subscription-manager.ts`:

- `class_reminders`: Upcoming class notifications
- `schedule_changes`: Class schedule modifications
- `new_members`: New member notifications
- `member_birthdays`: Birthday reminders
- `sub_requests`: Substitute request notifications
- `announcements`: General announcements
- `system_updates`: App and system updates
- `payment_reminders`: Payment-related notifications

### Custom Notification Handlers

#### Register Custom Handlers

```tsx
import { notificationHandlerService } from '~/lib/notification-handlers';

// Register a custom handler
notificationHandlerService.registerHandler({
  id: 'custom_handler',
  condition: (data) => data?.type === 'custom_event',
  action: {
    type: 'custom',
    payload: async (data, source) => {
      // Custom handling logic
      console.log('Custom notification received:', data);
      // Navigate, show modal, etc.
    },
  },
});
```

### UI Components

#### Notification Settings Component

```tsx
import { NotificationSettings } from '~/components/notifications/NotificationSettings';

function SettingsScreen() {
  return (
    <NotificationSettings
      onClose={() => {
        // Handle close
      }}
    />
  );
}
```

#### Notification Banner

```tsx
import { NotificationBanner } from '~/components/notifications/NotificationBanner';

function MyScreen() {
  const [showBanner, setShowBanner] = useState(false);

  return (
    <View>
      {/* Your content */}
      
      <NotificationBanner
        title="Class Reminder"
        message="Your yoga class starts in 15 minutes"
        type="warning"
        visible={showBanner}
        onDismiss={() => setShowBanner(false)}
        actionText="View Class"
        onActionPress={() => {
          // Navigate to class
          setShowBanner(false);
        }}
      />
    </View>
  );
}
```

## Testing

### Development Testing

#### Using the Testing Service

```tsx
import { notificationTestingService } from '~/lib/notification-testing';

// Run comprehensive test
const runTests = async () => {
  const results = await notificationTestingService.runComprehensiveTest();
  console.log('Test results:', results);
  
  // Get statistics
  const stats = notificationTestingService.getTestStatistics();
  console.log('Test statistics:', stats);
};

// Test specific templates
const testClassReminder = async () => {
  const result = await notificationTestingService.sendTestNotification('class_reminder');
  console.log('Class reminder test:', result);
};
```

#### Available Test Templates

- `class_reminder`: Class starting soon notification
- `new_member`: New member joined notification
- `schedule_change`: Class rescheduled notification
- `birthday`: Member birthday notification
- `sub_request`: Substitute request notification
- `announcement`: General announcement
- `system_error`: Error notification

### Production Testing

1. **Test with real devices**: Simulators don't support push notifications
2. **Test different app states**: Foreground, background, and quit states
3. **Test topic subscriptions**: Verify topic-based messaging works
4. **Test notification actions**: Ensure taps navigate correctly
5. **Test permissions**: Verify permission flow works properly

## Best Practices

### 1. Permission Management

- Always check permissions before sending notifications
- Provide clear explanations for why notifications are needed
- Handle permission denial gracefully
- Allow users to re-enable permissions later

### 2. Topic Organization

- Use descriptive topic names
- Group related notifications into topics
- Allow granular control over topic subscriptions
- Provide default subscriptions for essential notifications

### 3. Notification Content

- Keep titles concise and descriptive
- Provide actionable information in the body
- Include relevant data for navigation
- Use appropriate notification types (info, success, warning, error)

### 4. Performance

- Initialize services early in app lifecycle
- Handle network errors gracefully
- Cache subscription preferences locally
- Use background processing for heavy operations

### 5. Analytics

- Track notification delivery and engagement
- Monitor permission grant rates
- Analyze topic subscription patterns
- Track notification action completion

## Troubleshooting

### Common Issues

#### 1. Notifications Not Received

**Possible causes:**
- Permissions not granted
- Invalid or expired tokens
- Network connectivity issues
- App not properly configured

**Solutions:**
- Check permission status
- Refresh tokens
- Verify Firebase configuration
- Test with different networks

#### 2. Notifications Not Displayed in Foreground

**Possible causes:**
- Notification handler not set up
- App in focus (iOS behavior)
- Local notification not created

**Solutions:**
- Verify foreground handler is configured
- Create local notifications for foreground messages
- Check notification settings

#### 3. Topic Subscriptions Not Working

**Possible causes:**
- Network issues during subscription
- Invalid topic names
- Firebase configuration issues

**Solutions:**
- Retry subscription with error handling
- Verify topic names match server configuration
- Check Firebase console for topic activity

### Debug Mode

Enable debug logging by setting `__DEV__` flag:

```tsx
if (__DEV__) {
  console.log('Notification debug info:', {
    hasPermission,
    tokens,
    subscriptions,
  });
}
```

## Security Considerations

1. **Token Security**: Never log or expose push tokens in production
2. **Data Validation**: Validate notification data before processing
3. **Permission Respect**: Honor user notification preferences
4. **Privacy**: Don't include sensitive information in notifications
5. **Rate Limiting**: Implement client-side rate limiting for test notifications

## Performance Monitoring

Monitor these metrics:

- **Permission Grant Rate**: Percentage of users who grant notification permissions
- **Delivery Rate**: Percentage of notifications successfully delivered
- **Engagement Rate**: Percentage of notifications that are tapped
- **Topic Subscription Rate**: Popular topics and subscription patterns
- **Error Rate**: Frequency of notification-related errors

## Support

For issues or questions:

1. Check the troubleshooting section above
2. Review Firebase console for delivery status
3. Test with the built-in testing utilities
4. Check device notification settings
5. Verify network connectivity

## Changelog

### Version 1.0.0
- Initial implementation with Firebase and Expo integration
- Topic-based subscription management
- Comprehensive testing utilities
- Rich UI components
- Analytics integration
