import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export interface NotificationDetailData {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp?: string;
  data?: any;
  actionText?: string;
  onActionPress?: () => void;
  source?: string;
  priority?: 'low' | 'normal' | 'high';
  category?: string;
  imageUrl?: string;
}

interface NotificationDetailModalProps {
  visible: boolean;
  notification: NotificationDetailData | null;
  onClose: () => void;
  onActionPress?: () => void;
}

const { width, height } = Dimensions.get('window');

export const NotificationDetailModal: React.FC<NotificationDetailModalProps> = ({
  visible,
  notification,
  onClose,
  onActionPress,
}) => {
  const insets = useSafeAreaInsets();

  console.log("🔥 NotificationDetailModal render - visible:", visible);
  console.log("🔥 NotificationDetailModal render - notification:", notification);

  if (!notification) {
    console.log("🔥 NotificationDetailModal: No notification data, returning null");
    return null;
  }

  const getTypeStyles = () => {
    switch (notification.type) {
      case 'success':
        return {
          backgroundColor: '#10B981',
          iconName: 'checkmark-circle' as const,
          accentColor: '#059669',
        };
      case 'warning':
        return {
          backgroundColor: '#F59E0B',
          iconName: 'warning' as const,
          accentColor: '#D97706',
        };
      case 'error':
        return {
          backgroundColor: '#EF4444',
          iconName: 'alert-circle' as const,
          accentColor: '#DC2626',
        };
      default:
        return {
          backgroundColor: '#069CC3',
          iconName: 'information-circle' as const,
          accentColor: '#0891B2',
        };
    }
  };

  const typeStyles = getTypeStyles();

  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return 'Just now';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    
    return date.toLocaleDateString();
  };

  const renderDataSection = () => {
    if (!notification.data || Object.keys(notification.data).length === 0) {
      return null;
    }

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Additional Information</Text>
        <View style={styles.dataContainer}>
          {Object.entries(notification.data).map(([key, value]) => (
            <View key={key} style={styles.dataRow}>
              <Text style={styles.dataKey}>{key.replace(/_/g, ' ').toUpperCase()}:</Text>
              <Text style={styles.dataValue}>
                {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { paddingTop: insets.top }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: typeStyles.backgroundColor }]}>
          <View style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <Ionicons
                name={typeStyles.iconName}
                size={28}
                color="white"
                style={styles.headerIcon}
              />
              <View style={styles.headerTextContainer}>
                <Text style={styles.headerTitle} numberOfLines={2}>
                  {notification.title}
                </Text>
                <Text style={styles.headerTimestamp}>
                  {formatTimestamp(notification.timestamp)}
                </Text>
              </View>
            </View>
            
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel="Close notification details"
            >
              <Ionicons name="close" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Message Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Message</Text>
            <Text style={styles.messageText}>{notification.message}</Text>
          </View>

          {/* Metadata Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Details</Text>
            <View style={styles.metadataContainer}>
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>Type:</Text>
                <View style={[styles.typeBadge, { backgroundColor: typeStyles.accentColor }]}>
                  <Text style={styles.typeBadgeText}>{notification.type.toUpperCase()}</Text>
                </View>
              </View>
              
              {notification.category && (
                <View style={styles.metadataRow}>
                  <Text style={styles.metadataLabel}>Category:</Text>
                  <Text style={styles.metadataValue}>{notification.category}</Text>
                </View>
              )}
              
              {notification.priority && (
                <View style={styles.metadataRow}>
                  <Text style={styles.metadataLabel}>Priority:</Text>
                  <Text style={[
                    styles.metadataValue,
                    { color: notification.priority === 'high' ? '#EF4444' : '#6B7280' }
                  ]}>
                    {notification.priority.toUpperCase()}
                  </Text>
                </View>
              )}
              
              {notification.source && (
                <View style={styles.metadataRow}>
                  <Text style={styles.metadataLabel}>Source:</Text>
                  <Text style={styles.metadataValue}>{notification.source}</Text>
                </View>
              )}
              
              <View style={styles.metadataRow}>
                <Text style={styles.metadataLabel}>ID:</Text>
                <Text style={[styles.metadataValue, styles.idText]}>{notification.id}</Text>
              </View>
            </View>
          </View>

          {/* Additional Data Section */}
          {renderDataSection()}
        </ScrollView>

        {/* Actions */}
        <View style={styles.actionsContainer}>
          {notification.actionText && (onActionPress || notification.onActionPress) && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: typeStyles.backgroundColor }]}
              onPress={onActionPress || notification.onActionPress}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel={notification.actionText}
            >
              <Text style={styles.actionButtonText}>{notification.actionText}</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={styles.dismissButton}
            onPress={onClose}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Dismiss"
          >
            <Text style={styles.dismissButtonText}>Dismiss</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  headerLeft: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-start',
  },
  headerIcon: {
    marginTop: 2,
  },
  headerTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: '700',
    lineHeight: 26,
    marginBottom: 4,
  },
  headerTimestamp: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '500',
  },
  closeButton: {
    padding: 8,
    marginTop: -4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  metadataContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  metadataRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  metadataLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    flex: 1,
  },
  metadataValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    flex: 2,
    textAlign: 'right',
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  typeBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
  },
  idText: {
    fontFamily: 'monospace',
    fontSize: 12,
  },
  dataContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dataRow: {
    marginBottom: 12,
  },
  dataKey: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 4,
  },
  dataValue: {
    fontSize: 14,
    color: '#1F2937',
    fontFamily: 'monospace',
  },
  actionsContainer: {
    padding: 20,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  dismissButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
  },
  dismissButtonText: {
    color: '#6B7280',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NotificationDetailModal;