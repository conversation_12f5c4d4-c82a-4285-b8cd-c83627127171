import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Switch,
  TouchableOpacity,
  ScrollView,
  Alert,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNotifications } from '~/contexts/NotificationContext';
import { logEvent } from '~/lib/firebase';

interface NotificationSettingItem {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
  topic?: string;
  icon: keyof typeof Ionicons.glyphMap;
}

interface NotificationSettingsProps {
  onClose?: () => void;
}

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  onClose,
}) => {
  const {
    hasPermission,
    requestPermissions,
    subscribeToTopic,
    unsubscribeFromTopic,
    sendTestNotification,
    isLoading,
  } = useNotifications();

  const [settings, setSettings] = useState<NotificationSettingItem[]>([
    {
      id: 'class_reminders',
      title: 'Class Reminders',
      description: 'Get notified about upcoming classes',
      enabled: true,
      topic: 'class_reminders',
      icon: 'time',
    },
    {
      id: 'new_members',
      title: 'New Members',
      description: 'Notifications when new members join',
      enabled: true,
      topic: 'new_members',
      icon: 'person-add',
    },
    {
      id: 'schedule_changes',
      title: 'Schedule Changes',
      description: 'Updates about class schedule modifications',
      enabled: true,
      topic: 'schedule_changes',
      icon: 'calendar',
    },
    {
      id: 'birthdays',
      title: 'Member Birthdays',
      description: 'Birthday reminders for your members',
      enabled: false,
      topic: 'birthdays',
      icon: 'gift',
    },
    {
      id: 'announcements',
      title: 'Announcements',
      description: 'Important updates and announcements',
      enabled: true,
      topic: 'announcements',
      icon: 'megaphone',
    },
    {
      id: 'sub_requests',
      title: 'Sub Requests',
      description: 'Notifications about substitute requests',
      enabled: true,
      topic: 'sub_requests',
      icon: 'swap-horizontal',
    },
  ]);

  useEffect(() => {
    // Load saved settings from storage if needed
    loadSettings();
  }, []);

  const loadSettings = async () => {
    // You can implement loading settings from AsyncStorage here
    // For now, we'll use the default settings
  };

  const saveSettings = async (newSettings: NotificationSettingItem[]) => {
    // You can implement saving settings to AsyncStorage here
    setSettings(newSettings);
  };

  const handleToggleSetting = async (settingId: string) => {
    if (!hasPermission) {
      const granted = await requestPermissions();
      if (!granted) {
        Alert.alert(
          'Permission Required',
          'Please enable notifications in your device settings to receive updates.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => {/* Open settings */} },
          ]
        );
        return;
      }
    }

    const newSettings = settings.map(setting => {
      if (setting.id === settingId) {
        const newEnabled = !setting.enabled;
        
        // Subscribe/unsubscribe from topic
        if (setting.topic) {
          if (newEnabled) {
            subscribeToTopic(setting.topic).catch(console.error);
          } else {
            unsubscribeFromTopic(setting.topic).catch(console.error);
          }
        }

        // Log the change
        logEvent('notification_setting_changed', {
          setting_id: settingId,
          enabled: newEnabled,
          topic: setting.topic,
        });

        return { ...setting, enabled: newEnabled };
      }
      return setting;
    });

    await saveSettings(newSettings);
  };

  const handleTestNotification = async () => {
    try {
      await sendTestNotification(
        'Test Notification',
        'This is a test notification from Upace Connect!',
        { type: 'test' }
      );
      
      Alert.alert(
        'Test Sent',
        'A test notification has been sent. You should receive it shortly.',
        [{ text: 'OK' }]
      );

      await logEvent('test_notification_requested');
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to send test notification. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleRequestPermissions = async () => {
    const granted = await requestPermissions();
    
    if (granted) {
      Alert.alert(
        'Permissions Granted',
        'You will now receive push notifications.',
        [{ text: 'OK' }]
      );
    } else {
      Alert.alert(
        'Permissions Denied',
        'You can enable notifications later in your device settings.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Notification Settings</Text>
        {onClose && (
          <TouchableOpacity
            onPress={onClose}
            style={styles.closeButton}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Close notification settings"
          >
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {!hasPermission && (
        <View style={styles.permissionCard}>
          <Ionicons name="notifications-off" size={32} color="#F59E0B" />
          <Text style={styles.permissionTitle}>Enable Notifications</Text>
          <Text style={styles.permissionDescription}>
            Allow notifications to stay updated with important information about your classes and members.
          </Text>
          <TouchableOpacity
            style={styles.permissionButton}
            onPress={handleRequestPermissions}
            disabled={isLoading}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Enable notifications"
          >
            <Text style={styles.permissionButtonText}>
              {isLoading ? 'Requesting...' : 'Enable Notifications'}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Notification Types</Text>
        <Text style={styles.sectionDescription}>
          Choose which types of notifications you'd like to receive.
        </Text>

        {settings.map((setting) => (
          <View key={setting.id} style={styles.settingItem}>
            <View style={styles.settingIcon}>
              <Ionicons
                name={setting.icon}
                size={20}
                color={setting.enabled ? '#069CC3' : '#999'}
              />
            </View>
            
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>{setting.title}</Text>
              <Text style={styles.settingDescription}>{setting.description}</Text>
            </View>

            <Switch
              value={setting.enabled}
              onValueChange={() => handleToggleSetting(setting.id)}
              trackColor={{ false: '#E5E7EB', true: '#069CC3' }}
              thumbColor={setting.enabled ? '#ffffff' : '#ffffff'}
              disabled={!hasPermission}
              accessible={true}
              accessibilityRole="switch"
              accessibilityLabel={`${setting.title} notifications`}
              accessibilityState={{ checked: setting.enabled }}
            />
          </View>
        ))}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Testing</Text>
        <TouchableOpacity
          style={[
            styles.testButton,
            (!hasPermission || isLoading) && styles.testButtonDisabled,
          ]}
          onPress={handleTestNotification}
          disabled={!hasPermission || isLoading}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="Send test notification"
        >
          <Ionicons name="send" size={20} color={hasPermission ? '#069CC3' : '#999'} />
          <Text style={[
            styles.testButtonText,
            (!hasPermission || isLoading) && styles.testButtonTextDisabled,
          ]}>
            Send Test Notification
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  permissionCard: {
    margin: 20,
    padding: 20,
    backgroundColor: '#FEF3C7',
    borderRadius: 12,
    alignItems: 'center',
  },
  permissionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#92400E',
    marginTop: 12,
    marginBottom: 8,
  },
  permissionDescription: {
    fontSize: 14,
    color: '#92400E',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  permissionButton: {
    backgroundColor: '#F59E0B',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
    lineHeight: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
    marginRight: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#F0F9FF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#069CC3',
  },
  testButtonDisabled: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
  },
  testButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#069CC3',
    marginLeft: 8,
  },
  testButtonTextDisabled: {
    color: '#9CA3AF',
  },
});

export default NotificationSettings;
