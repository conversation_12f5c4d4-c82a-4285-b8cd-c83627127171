import React, { useState, useEffect, ReactNode } from "react";
import { View, StyleSheet } from "react-native";
import * as Notifications from "expo-notifications";
import { NotificationBanner } from "./NotificationBanner";
import {
  NotificationDetailModal,
  NotificationDetailData,
} from "./NotificationDetailModal";

import { logEvent } from "~/lib/firebase";
import { useNotification } from "~/contexts/NotificationContext";

interface NotificationData {
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  data?: any;
  actionText?: string;
  onActionPress?: () => void;
  duration?: number;
}

interface NotificationManagerProps {
  children: ReactNode;
}

export const NotificationManager: React.FC<NotificationManagerProps> = ({
  children,
}) => {
  const [currentNotification, setCurrentNotification] =
    useState<NotificationData | null>(null);
  const [notificationQueue, setNotificationQueue] = useState<
    NotificationData[]
  >([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedNotification, setSelectedNotification] =
    useState<NotificationDetailData | null>(null);
  const { notification, notificationResponse, error } = useNotification();

  console.log({ notification });

  // Handle foreground notifications
  useEffect(() => {
    if (notification) {
      handleForegroundNotification(notification);
    }
  }, [notification]);

  // Handle notification responses (when user taps notification)
  useEffect(() => {
    if (notificationResponse) {
      handleNotificationResponse(notificationResponse);
    }
  }, [notificationResponse]);

  const handleForegroundNotification = async (
    notification: Notifications.Notification
  ) => {
    const { title, body, data } = notification.request.content;

    // Create notification data
    const notificationData: NotificationData = {
      id: notification.request.identifier,
      title: title || "New Notification",
      message: body || "You have a new message",
      type: getNotificationType(data),
      data,
      actionText: getActionText(data),
      onActionPress: () => handleNotificationAction(data),
      duration: 5000,
    };

    // Add to queue or show immediately
    if (currentNotification) {
      setNotificationQueue((prev) => [...prev, notificationData]);
    } else {
      setCurrentNotification(notificationData);
    }

    // Log the event
    await logEvent("notification_displayed_foreground", {
      notification_id: notification.request.identifier,
      type: notificationData.type,
      has_action: !!notificationData.actionText,
    });
  };

  const handleNotificationResponse = async (
    response: Notifications.NotificationResponse
  ) => {
    const { title, body, data } = response.notification.request.content;

    // Create detailed notification data and show modal immediately
    const detailData: NotificationDetailData = {
      id: response.notification.request.identifier,
      title: title || "Notification",
      message: body || "You have a new notification",
      type: getNotificationType(data),
      timestamp: new Date(response.notification.date).toISOString(),
      data,
      actionText: getActionText(data),
      onActionPress: () => handleNotificationAction(data),
      source: "Push Notification",
      priority: getNotificationType(data) === "error" ? "high" : "normal",
      category: getNotificationCategory(data),
    };

    setSelectedNotification(detailData);
    setDetailModalVisible(true);

    // Log the event
    await logEvent("notification_tapped", {
      notification_id: response.notification.request.identifier,
      action_type: response.actionIdentifier,
    });
  };

  const getNotificationType = (
    data: any
  ): "info" | "success" | "warning" | "error" => {
    if (data?.type) {
      switch (data.type) {
        case "success":
        case "class_confirmed":
        case "member_joined":
          return "success";
        case "warning":
        case "class_reminder":
        case "schedule_change":
          return "warning";
        case "error":
        case "class_cancelled":
          return "error";
        default:
          return "info";
      }
    }
    return "info";
  };

  const getActionText = (data: any): string | undefined => {
    if (data?.type) {
      switch (data.type) {
        case "class_reminder":
          return "View Class";
        case "new_member":
          return "View Member";
        case "schedule_change":
          return "View Schedule";
        case "birthday":
          return "Send Wishes";
        case "sub_request":
          return "View Request";
        default:
          return "View";
      }
    }
    return undefined;
  };

  const getNotificationCategory = (data: any): string => {
    if (data?.type) {
      switch (data.type) {
        case "class_reminder":
        case "class_confirmed":
        case "class_cancelled":
        case "schedule_change":
          return "Class Management";
        case "new_member":
        case "member_joined":
        case "birthday":
          return "Member Activity";
        case "sub_request":
          return "Substitution";
        default:
          return "General";
      }
    }
    return "General";
  };

  const handleNotificationAction = (data: any) => {
    // This would typically navigate to the appropriate screen
    // You can implement navigation logic here based on the data
    console.log("Notification action:", data);
  };

  const handleNotificationPress = () => {
    console.log("🔥 handleNotificationPress called");
    console.log("🔥 currentNotification:", currentNotification);

    if (currentNotification) {
      // Convert to NotificationDetailData format and show detail modal
      const detailData: NotificationDetailData = {
        id: currentNotification.id,
        title: currentNotification.title,
        message: currentNotification.message,
        type: currentNotification.type,
        timestamp: new Date().toISOString(),
        data: currentNotification.data,
        actionText: currentNotification.actionText,
        onActionPress: currentNotification.onActionPress,
        source: "Push Notification",
        priority: currentNotification.type === "error" ? "high" : "normal",
        category: getNotificationCategory(currentNotification.data),
      };

      console.log("🔥 Setting detail modal visible with data:", detailData);
      setSelectedNotification(detailData);
      setDetailModalVisible(true);
      // Don't dismiss the notification immediately, let the modal close handler do it
      setCurrentNotification(null);
    } else {
      console.log("🔥 No currentNotification available");
    }
  };

  const handleNotificationDismiss = () => {
    setCurrentNotification(null);

    // Process next notification in queue
    setTimeout(() => {
      if (notificationQueue.length > 0) {
        const nextNotification = notificationQueue[0];
        setNotificationQueue((prev) => prev.slice(1));
        setCurrentNotification(nextNotification);
      }
    }, 300);
  };

  const handleDetailModalClose = () => {
    console.log("🔥 handleDetailModalClose called");
    setDetailModalVisible(false);
    setSelectedNotification(null);

    // Process next notification in queue after modal closes
    setTimeout(() => {
      if (notificationQueue.length > 0) {
        const nextNotification = notificationQueue[0];
        setNotificationQueue((prev) => prev.slice(1));
        setCurrentNotification(nextNotification);
      }
    }, 300);
  };

  const handleDetailModalAction = () => {
    if (selectedNotification?.onActionPress) {
      selectedNotification.onActionPress();
    }
    handleDetailModalClose();
  };

  const handleActionPress = () => {
    if (currentNotification?.onActionPress) {
      currentNotification.onActionPress();
    }
    handleNotificationDismiss();
  };

  // Public methods for showing custom notifications
  const showNotification = (notification: Omit<NotificationData, "id">) => {
    const notificationWithId: NotificationData = {
      ...notification,
      id: Date.now().toString(),
    };

    if (currentNotification) {
      setNotificationQueue((prev) => [...prev, notificationWithId]);
    } else {
      setCurrentNotification(notificationWithId);
    }
  };

  const showSuccess = (
    title: string,
    message: string,
    actionText?: string,
    onActionPress?: () => void
  ) => {
    showNotification({
      title,
      message,
      type: "success",
      actionText,
      onActionPress,
    });
  };

  const showError = (
    title: string,
    message: string,
    actionText?: string,
    onActionPress?: () => void
  ) => {
    showNotification({
      title,
      message,
      type: "error",
      actionText,
      onActionPress,
    });
  };

  const showWarning = (
    title: string,
    message: string,
    actionText?: string,
    onActionPress?: () => void
  ) => {
    showNotification({
      title,
      message,
      type: "warning",
      actionText,
      onActionPress,
    });
  };

  const showInfo = (
    title: string,
    message: string,
    actionText?: string,
    onActionPress?: () => void
  ) => {
    showNotification({
      title,
      message,
      type: "info",
      actionText,
      onActionPress,
    });
  };

  return (
    <View style={styles.container}>
      {children}

      {currentNotification && (
        <NotificationBanner
          title={currentNotification.title}
          message={currentNotification.message}
          type={currentNotification.type}
          visible={!!currentNotification}
          duration={currentNotification.duration}
          onPress={handleNotificationPress}
          onDismiss={handleNotificationDismiss}
          actionText={currentNotification.actionText}
          onActionPress={
            currentNotification.actionText ? handleActionPress : undefined
          }
        />
      )}

      <NotificationDetailModal
        visible={detailModalVisible}
        notification={selectedNotification}
        onClose={handleDetailModalClose}
        onActionPress={handleDetailModalAction}
      />
    </View>
  );
};

// Export helper functions for use throughout the app
export const NotificationHelpers = {
  showSuccess: (
    title: string,
    message: string,
    actionText?: string,
    onActionPress?: () => void
  ) => {
    // This would need to be connected to the NotificationManager instance
    console.log("Success notification:", { title, message, actionText });
  },
  showError: (
    title: string,
    message: string,
    actionText?: string,
    onActionPress?: () => void
  ) => {
    console.log("Error notification:", { title, message, actionText });
  },
  showWarning: (
    title: string,
    message: string,
    actionText?: string,
    onActionPress?: () => void
  ) => {
    console.log("Warning notification:", { title, message, actionText });
  },
  showInfo: (
    title: string,
    message: string,
    actionText?: string,
    onActionPress?: () => void
  ) => {
    console.log("Info notification:", { title, message, actionText });
  },
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default NotificationManager;
