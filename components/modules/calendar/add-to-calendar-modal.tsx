import React from "react";
import { View, TouchableOpacity } from "react-native";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { addAppointmentToCalendar } from "~/modules/calendar/calendar-utils";
import { showSuccessToast, showErrorToast } from "~/components/toast";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { Ionicons } from "@expo/vector-icons";
import { saveCalendarEventId } from "~/modules/calendar/calendar-storage";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";

interface AddToCalendarModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  reservationDetails: {
    title: string;
    startTime: string;
    endTime: string;
    location?: string;
    notes?: string;
    id?: number | string;
    type?: string;
  };
}

export function AddToCalendarModal({
  isOpen,
  setIsOpen,
  reservationDetails,
}: AddToCalendarModalProps) {
  const { trackEvent, EVENTS } = useAnalytics();
  const [isAdding, setIsAdding] = React.useState(false);

  // Add to Calendar implementation
  const handleAddToCalendar = async () => {
    try {
      setIsAdding(true);

      const eventId = await addAppointmentToCalendar(
        reservationDetails.title,
        reservationDetails.startTime,
        reservationDetails.endTime,
        reservationDetails.location || "",
        reservationDetails.notes || ""
      );

      if (eventId) {
        showSuccessToast("Added to calendar successfully");

        // Save the calendar event ID for this appointment
        if (reservationDetails.id) {
          await saveCalendarEventId(reservationDetails.id, eventId);
        }

        // Track the event
        trackEvent(EVENTS.ADD_TO_CALENDAR, {
          appointment_id: reservationDetails.id,
          type: reservationDetails.type || "appointment",
          event_id: eventId,
        });

        // Close the modal after a short delay to allow the toast to be seen
        setTimeout(() => {
          setIsOpen(false);
        }, 1000);
      } else {
        // If no eventId was returned, keep the modal open
        setIsAdding(false);
      }
    } catch (err: unknown) {
      const error = err as Error;
      console.error("Error adding to calendar:", error);
      showErrorToast(
        "Failed to add to calendar: " + (error.message || "Unknown error")
      );
      setIsAdding(false);
    }
  };

  return (
    <ModalBottomSheet
      isVisible={isOpen}
      onClose={() => setIsOpen(false)}
      title="Share"
      height={350}
    >
      <View className="flex-1">
        <View className="items-center justify-center mb-6">
          <View className="bg-indigo-100 p-3 rounded-full mb-3">
            <Ionicons name="calendar-outline" size={28} color="#4338ca" />
          </View>
          <Text
            className="text-gray-600 text-center text-sm"
            accessibilityLabel="Share this appointment"
            accessibilityRole="text"
          >
            Share this appointment
          </Text>
        </View>

        <View className="mt-4 gap-3">
          <View className="flex-row flex-wrap gap-4 justify-center">
            {/* Calendar sharing option */}
            <TouchableOpacity
              onPress={handleAddToCalendar}
              disabled={isAdding}
              accessibilityRole="button"
              accessibilityLabel="Add to calendar"
              accessibilityHint="Adds this appointment to your device calendar"
              className="items-center"
            >
              <View className="w-[60px] h-[60px] bg-indigo-600 rounded-lg items-center justify-center mb-1">
                {isAdding ? (
                  <View className="items-center justify-center">
                    <Text className="text-white text-xs">Adding...</Text>
                  </View>
                ) : (
                  <Ionicons name="calendar-outline" size={28} color="white" />
                )}
              </View>
              <Text className="text-xs text-center">Calendar</Text>
            </TouchableOpacity>

            {/* Space for additional sharing buttons in the future */}
          </View>

          <Button
            onPress={() => {
              // Track the skip event
              trackEvent(EVENTS.SKIP_ADD_TO_CALENDAR, {
                appointment_id: reservationDetails.id,
                type: reservationDetails.type || "appointment",
              });
              setIsOpen(false);
            }}
            variant="secondary"
            className="w-full mt-4"
            label="Cancel"
            accessibilityLabel="Cancel sharing"
            accessibilityRole="button"
            accessibilityHint="Closes this sharing dialog"
          />
        </View>
      </View>
    </ModalBottomSheet>
  );
}
