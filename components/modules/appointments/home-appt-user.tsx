import { View } from "react-native";
import { Card, CardContent } from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { getInitials } from "~/modules/classes/utils";
import { Fragment, ReactNode } from "react";
import { BaseAvatar } from "../classes/avatar";
import { HomeMembershipControl } from "./home-membership-control";
import { CancelControl } from "./cancel-control";

import Ionicons from "@expo/vector-icons/Ionicons";

export function HomeApptUser({
  name,
  isCheckIn,
  id,
  image,
  extraActions,
  date,
  membership_id,
  equipment_id,
  user_id,
  onRefresh,
  isDecremented,
}: {
  name: string;
  isCheckIn: boolean;
  date?: string;
  id: number;
  image?: string;
  extraActions?: ReactNode;
  membership_id?: string | null;
  equipment_id?: number | string;
  user_id?: number;
  onRefresh?: () => void;
  isDecremented?: boolean;
}) {
  const hasMembership = Boolean(membership_id);

  // Create comprehensive accessibility label for the member card
  const memberCardLabel = `Member: ${name}${
    isDecremented
      ? ", checked in and decremented"
      : isCheckIn
      ? ", checked in"
      : ", not checked in"
  }${!hasMembership && !isDecremented ? ", membership not assigned" : ""}`;

  return (
    <Fragment>
      <Card className="w-full mt-4" accessibilityLabel={memberCardLabel}>
        <CardContent className="p-0 flex flex-row justify-between">
          <View
            className="flex flex-row gap-2 p-3"
            accessibilityLabel={`Member information for ${name}`}
          >
            <BaseAvatar
              url={image ?? ""}
              name={getInitials(name)}
              accessibilityLabel={`Profile picture for ${name}`}
            />
            <Text
              className="font-bold mt-2"
              accessibilityRole="text"
              accessibilityLabel={`Member name: ${name}`}
            >
              {name}
            </Text>
          </View>
          {isDecremented ? (
            <View
              className="flex flex-row items-center gap-4 pr-3"
              accessibilityLabel="Member status: checked in and decremented"
            >
              <View
                className="flex flex-row gap-1"
                accessibilityLabel="Checked in status"
              >
                <Ionicons
                  name="checkmark-done"
                  size={15}
                  color="#22c55e"
                  accessibilityLabel="Check mark icon"
                />
                <Text
                  className="text-xs text-green-500 font-bold items-center"
                  accessibilityLabel="Member is checked in"
                >
                  Checked in
                </Text>
              </View>

              <View
                className="flex flex-row gap-1"
                accessibilityLabel="Decremented status"
              >
                <Ionicons
                  name="checkmark-done"
                  size={15}
                  color="#3b82f6"
                  accessibilityLabel="Check mark icon"
                />
                <Text
                  className="text-xs text-blue-500 font-bold items-center"
                  accessibilityLabel="Membership has been decremented"
                >
                  Decremented
                </Text>
              </View>
            </View>
          ) : (
            <View
              className="flex flex-row items-center gap-4"
              accessibilityLabel={`Actions for ${name}: ${
                hasMembership ? "check-in toggle" : "assign membership"
              } and cancel appointment`}
            >
              <HomeMembershipControl
                id={id}
                isCheckIn={isCheckIn}
                membership_id={membership_id}
                equipment_id={equipment_id}
                user_id={user_id}
                onRefresh={onRefresh}
                date={date}
              />
              {extraActions}
              <CancelControl id={id} isChecked={isCheckIn} type="appointment" />
            </View>
          )}
        </CardContent>
      </Card>
    </Fragment>
  );
}
