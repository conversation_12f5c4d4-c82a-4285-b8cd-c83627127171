import React, { useState } from "react";
import { View, ScrollView } from "react-native";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";
import { useUserMembership } from "~/modules/classes/queries/useUserMembership";
import { ComboBox } from "../common/Combo-box";
import {
  AutocompleteDropdownContextProvider,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";
import { useUpdateReservation } from "~/modules/classes/mutations/useUpdateReservation";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { showSuccessToast } from "~/components/toast";

interface MembershipAssignModalProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  reservationId: number;
  userId: number;
  equipmentId: number | string;
  onSuccess?: () => void;
  date?: string;
}

export function MembershipAssignModal({
  isOpen,
  setIsOpen,
  reservationId,
  userId,
  equipmentId,
  onSuccess,
  date,
}: MembershipAssignModalProps) {
  const { trackEvent, EVENTS } = useAnalytics();
  const [selectedMembership, setSelectedMembership] =
    useState<AutocompleteDropdownItem | null>(null);

  const { data: membershipData = [], isLoading } = useUserMembership({
    itemId: String(equipmentId),
    userId: String(userId),
    type: "pt",
    date: date as string,
  });

  const { mutate: updateReservation, isPending } = useUpdateReservation(() => {
    setIsOpen(false);
    onSuccess?.();
    showSuccessToast("Membership assigned successfully");
  });

  const membershipOptions = React.useMemo(
    () =>
      membershipData.map((rec) => ({
        id: String(rec.id),
        title: rec.display_text,
      })),
    [membershipData]
  );

  const handleAssignMembership = () => {
    if (!selectedMembership) return;

    updateReservation({
      reservation_id: reservationId,
      membership_id: selectedMembership.id,
    });

    // Track membership assignment event
    trackEvent(EVENTS.ASSIGN_MEMBERSHIP, {
      reservation_id: reservationId,
      user_id: userId,
      equipment_id: equipmentId,
      membership_id: selectedMembership.id,
      membership_name: selectedMembership.title,
    });
  };

  return (
    <ModalBottomSheet
      isVisible={isOpen}
      onClose={() => setIsOpen(false)}
      title="Assign Membership"
      height={400}
    >
      <ScrollView className="flex-1">
        <View className="mb-4">
          <Text
            className="text-center text-sm text-gray-600"
            accessibilityLabel="Select a membership to assign to this appointment"
            accessibilityRole="text"
          >
            Select a membership to assign to this appointment
          </Text>
        </View>

        <AutocompleteDropdownContextProvider>
          <Text
            className="text-sm font-medium mb-2"
            accessibilityLabel="Membership selection"
            accessibilityRole="text"
          >
            Membership
          </Text>
          <View className="flex h-14 mb-6">
            <ComboBox
              isLoading={isLoading}
              direction="up"
              data={membershipOptions}
              placeholder="Select membership"
              onSelect={(item) => {
                if (item) {
                  setSelectedMembership(item);
                }
              }}
              value={selectedMembership?.title ?? ""}
              emptyText="No available membership to select"
              accessibilityLabel="Membership selection dropdown"
              accessibilityHint="Select a membership package to assign to this member"
            />
          </View>
        </AutocompleteDropdownContextProvider>

        <View className="flex flex-row mt-4 gap-3">
          <Button
            onPress={() => setIsOpen(false)}
            variant="secondary"
            className="flex-1"
            label="Cancel"
            accessibilityLabel="Cancel assignment"
            accessibilityRole="button"
            accessibilityHint="Cancels membership assignment and closes this dialog"
          />
          <Button
            onPress={handleAssignMembership}
            disabled={!selectedMembership || isPending}
            isLoading={isPending}
            className="flex-1 bg-[#002966]"
            label="Assign"
            accessibilityLabel="Assign membership"
            accessibilityRole="button"
            accessibilityHint="Assigns the selected membership to this appointment"
          />
        </View>
      </ScrollView>
    </ModalBottomSheet>
  );
}
