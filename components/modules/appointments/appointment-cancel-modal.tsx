import React, { useState, useEffect } from "react";
import { View } from "react-native";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useCancelReservation } from "~/modules/classes/mutations/useCancelReservation";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import {
  getCalendarEventId,
  removeCalendarEventId,
} from "~/modules/calendar/calendar-storage";
import { deleteCalendarEvent } from "~/modules/calendar/calendar-utils";
import { showSuccessToast } from "~/components/toast";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";

interface AppointmentCancelModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  appointmentId: number;
}

export function AppointmentCancelModal({
  isOpen,
  setIsOpen,
  appointmentId,
}: AppointmentCancelModalProps) {
  const { trackEvent, EVENTS } = useAnalytics();
  // Calendar functionality restored
  const [calendarEventId, setCalendarEventId] = useState<string | null>(null);
  const [isLoadingCalendarId, setIsLoadingCalendarId] = useState(true);

  // Fetch the calendar event ID when the modal opens
  useEffect(() => {
    if (isOpen) {
      const fetchCalendarEventId = async () => {
        setIsLoadingCalendarId(true);
        const eventId = await getCalendarEventId(appointmentId);
        setCalendarEventId(eventId);
        setIsLoadingCalendarId(false);
      };

      fetchCalendarEventId();
    }
  }, [isOpen, appointmentId]);

  const { mutate: handleCancellation, isPending: isCancelling } =
    useCancelReservation(async () => {
      // Calendar functionality restored
      // If there's a calendar event ID, delete the calendar event
      if (calendarEventId) {
        const deleted = await deleteCalendarEvent(calendarEventId);
        if (deleted) {
          // Remove the calendar event ID from storage
          await removeCalendarEventId(appointmentId);
          showSuccessToast("Appointment and calendar event deleted");
        } else {
          showSuccessToast(
            "Appointment deleted, but calendar event could not be deleted"
          );
        }
      } else {
        showSuccessToast("Appointment deleted");
      }

      // Track the deletion event
      trackEvent(EVENTS.DELETE_APPOINTMENT, {
        appointment_id: appointmentId,
        calendar_event_deleted: Boolean(calendarEventId),
      });

      setIsOpen(false);
    });

  return (
    <ModalBottomSheet
      isVisible={isOpen}
      onClose={() => setIsOpen(false)}
      title="Cancel Appointment?"
      height={300}
    >
      <View className="flex-1">
        <View className="mb-6">
          <Text
            className="text-center text-red-500 font-medium"
            accessibilityLabel="This will cancel the appointment permanently and cannot be undone"
            accessibilityRole="text"
          >
            This will cancel the appointment permanently and cannot be undone.
          </Text>
          {calendarEventId && !isLoadingCalendarId && (
            <Text
              className="text-gray-600 mt-2 text-sm text-center"
              accessibilityLabel="The appointment will also be removed from your calendar"
              accessibilityRole="text"
            >
              The appointment will also be removed from your calendar.
            </Text>
          )}
        </View>

        <View className="flex-row gap-3 mt-4">
          <Button
            onPress={() => setIsOpen(false)}
            variant="secondary"
            className="flex-1"
            label="Keep"
            accessibilityLabel="Keep appointment"
            accessibilityRole="button"
            accessibilityHint="Keeps the appointment and closes this dialog"
          />
          <Button
            onPress={() => handleCancellation(appointmentId)}
            disabled={isCancelling}
            variant="destructive"
            className="flex-1"
            label={isCancelling ? "Cancelling..." : "Cancel"}
            accessibilityLabel={
              isCancelling ? "Cancelling appointment" : "Cancel appointment"
            }
            accessibilityRole="button"
            accessibilityHint="Permanently cancels this appointment"
          />
        </View>
      </View>
    </ModalBottomSheet>
  );
}
