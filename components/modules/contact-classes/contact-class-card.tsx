import { Alert, TouchableOpacity, View } from "react-native";
import { Card, CardContent } from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { format, parseISO } from "date-fns";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { ContactClass } from "~/modules/contact-classes/types";
import { formatTime } from "~/modules/classes/utils";
import { useDeleteContactClass } from "~/modules/contact-classes/mutations/useDeleteContactClass";

const status = (sent: boolean, cancelled: boolean) => {
  if (sent)
    return {
      label: "Sent",
      color: "text-green-500",
    };
  if (cancelled)
    return {
      label: "Cancelled",
      color: "text-red-500",
    };
  return {
    label: "Pending",
    color: "text-orange-500",
  };
};

export function ContactClassCard({
  class_name,
  class_start_time,
  gym_name,
  reason,
  date,
  sent,
  contact_class_id,
  cancelled,
}: ContactClass) {
  const formattedDate = format(parseISO(date), "EEE MMM d, yyyy");

  const { mutate: deleteContactClass } = useDeleteContactClass();

  const handleDelete = () => {
    Alert.alert(
      "Confirm Delete",
      `Are you sure you want to delete the contact class for "${class_name}"?`,
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          onPress: () => {
            deleteContactClass(contact_class_id);
          },
        },
      ]
    );
  };

  return (
    <Card className="mb-3 relative">
      <TouchableOpacity
        onPress={handleDelete}
        className="absolute top-2 right-2 z-10 p-2"
        accessibilityRole="button"
        accessibilityLabel={`Delete contact class for ${class_name}`}
        accessibilityHint={`Deletes the contact class notification for ${class_name} on ${formattedDate}`}
      >
        <MaterialIcons name="delete" size={20} color="#ef4444" />
      </TouchableOpacity>

      <CardContent className="pt-4 pr-12">
        <View className="flex flex-row gap-2 w-[95%]">
          <Text className="font-bold">Class:</Text>
          <Text className="w-[95%] text-gray-700 dark:text-gray-300">
            {class_name} - {gym_name}
          </Text>
        </View>
        <View className="space-y-2">
          <View className="flex flex-row items-center gap-2">
            <Text className="font-bold">Date:</Text>
            <Text className="text-gray-600 dark:text-gray-400">{`${formattedDate}, ${formatTime(
              class_start_time
            )}`}</Text>
          </View>

          <View className="flex flex-row whitespace-pre-wrap w-[90%]">
            <Text className="font-bold  dark:text-gray-300 mb-1 whitespace-nowrap">
              Reason:
            </Text>
            <Text className="text-gray-600 dark:text-gray-400 pl-2 whitespace-break-spaces">
              {reason}
            </Text>
          </View>

          <View className="flex flex-row items-center gap-2">
            <Text className="font-bold">Status:</Text>
            <Text
              className={`font-bold text-gray-600 dark:text-gray-400 ${
                status(Boolean(sent), Boolean(cancelled)).color
              }`}
            >
              {status(Boolean(sent), Boolean(cancelled)).label}
            </Text>
          </View>
        </View>
      </CardContent>
    </Card>
  );
}
