import React from "react";
import { <PERSON>, ScrollView } from "react-native";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "~/components/ui/button";
import {
  Form,
  FormField,
  FormLabel,
  FormMessage,
  FormInput,
} from "~/components/ui/form";
import { Text } from "~/components/ui/text";
import { DatePicker } from "~/components/modules/common/date-picker";
import { useFocusEffect } from "@react-navigation/native";

import { MultiComboBox } from "~/components/modules/common/Multi-combo-box";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import {
  AutocompleteDropdownContextProvider,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";
import { useClassesData } from "~/modules/classes/queries/useClassesQuery";
import { formatClassDate } from "~/modules/classes/utils";
import { TimePicker } from "../common/time-picker";
import { formatClassItem } from "../cancelled-classes/cancel-class-form";
import { sub } from "date-fns";

export const ContactClassSchema = z.object({
  date: z.date({
    message: "Date is required",
  }),
  classes: z
    .array(
      z.object({
        id: z.string(),
        title: z.string(),
      })
    )
    .min(1, "At least one class must be selected"),
  reason: z.string().min(1, "Reason is required"),
  send_at_date: z.date({
    message: "Date is required",
  }),
  time: z.date({
    message: "Time is required",
  }),
});

export type ContactClassFormSchema = z.infer<typeof ContactClassSchema>;

interface CancelClassFormProps {
  onSubmit: (data: ContactClassFormSchema) => void;
  isLoading?: boolean;
}

export const ContactClassForm = ({
  onSubmit,
  isLoading,
}: CancelClassFormProps) => {
  const form = useForm<ContactClassFormSchema>({
    resolver: zodResolver(ContactClassSchema),
    defaultValues: {
      date: new Date(),
      classes: [],
      reason: "",
      send_at_date: new Date(),
      time: new Date(),
    },
  });

  // Reset form when screen comes into focus to ensure clean state
  useFocusEffect(
    React.useCallback(() => {
      // Reset form to clean state when screen gains focus
      form.reset({
        date: new Date(),
        classes: [],
        reason: "",
        send_at_date: new Date(),
      });
    }, [form])
  );

  const selectedValues = useWatch({
    control: form.control,
  });

  const { data: classesData = [], isPending } = useClassesData({
    date: formatClassDate(selectedValues.date),
  });

  const classOptions: AutocompleteDropdownItem[] = formatClassItem(
    classesData,
    "id"
  );

  const resetClassesValue = () => {
    form.setValue("classes", [], { shouldDirty: true });
  };

  return (
    <AutocompleteDropdownContextProvider>
      <View className="flex-1">
        <Form {...form}>
          <KeyboardAwareScrollView
            bottomOffset={10}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingTop: 16,
              paddingBottom: 100,
            }}
            className="flex-1"
          >
            <ScrollView className="space-y-4">
              <View className="space-y-4">
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <View className="space-y-3">
                      <DatePicker
                        minDate={sub(new Date(), { months: 3 })}
                        value={field.value}
                        onChange={(date) => {
                          field.onChange(date);
                          resetClassesValue();
                        }}
                        label="Date of class*"
                      />
                      <FormMessage />
                    </View>
                  )}
                />

                <FormField
                  control={form.control}
                  name="classes"
                  render={({ field }) => (
                    <View className="space-y-2">
                      <FormLabel>Classes* (select one or more)</FormLabel>
                      <View style={{ minHeight: 50 }}>
                        <MultiComboBox
                          data={classOptions}
                          //@ts-expect-error
                          key={field.value}
                          placeholder="Select classes to contact"
                          selectedItems={field.value}
                          onSelect={(items: AutocompleteDropdownItem[]) => {
                            field.onChange(items);
                          }}
                        />
                      </View>
                      <FormMessage />
                      {isPending && (
                        <Text className="text-sm text-gray-500">
                          Loading classes...
                        </Text>
                      )}
                    </View>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <View className="space-y-2 mt-4">
                      <FormLabel>Message*</FormLabel>
                      <FormInput
                        placeholder="Enter message to send to the class participants (Limit: 375 characters)"
                        value={field.value}
                        onChangeText={field.onChange}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        multiline={true}
                        numberOfLines={4}
                        style={{
                          height: 100,
                          textAlignVertical: "top",
                        }}
                      />
                    </View>
                  )}
                />

                <View className="flex flex-row gap-2">
                  <FormField
                    control={form.control}
                    name="send_at_date"
                    render={({ field }) => (
                      <View className="w-[60%]">
                        <DatePicker
                          mode="date"
                          value={field.value}
                          onChange={(date) => field.onChange(date)}
                          label="Date to Send"
                        />
                        <FormMessage />
                      </View>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="time"
                    render={({ field }) => (
                      <View className="w-[40%]">
                        <TimePicker
                          selectedDate={
                            selectedValues.send_at_date || new Date()
                          }
                          value={field.value}
                          onChange={(date) => field.onChange(date)}
                          label="Time"
                        />
                        <FormMessage />
                      </View>
                    )}
                  />
                </View>

                <View className="pt-4">
                  <Button
                    onPress={form.handleSubmit(onSubmit)}
                    disabled={isLoading}
                    className="w-full"
                    label={isLoading ? "Cancelling..." : "Save"}
                    isLoading={isLoading}
                  />
                </View>
              </View>
            </ScrollView>
          </KeyboardAwareScrollView>
        </Form>
      </View>
    </AutocompleteDropdownContextProvider>
  );
};
