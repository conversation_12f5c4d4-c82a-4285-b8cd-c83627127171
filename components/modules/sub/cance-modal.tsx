import { View } from "react-native";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useCancelRequest } from "~/modules/sub-management/mutations/useCancelRequest";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";

export function CancelSubRequestModal({
  isOpen,
  setIsOpen,
  id,
  className,
  requestDate,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  id: number;
  className?: string;
  requestDate?: string;
}) {
  const { trackEvent, EVENTS } = useAnalytics();

  const { mutate: handleCancellation, isPending } = useCancelRequest(() => {
    setIsOpen(false);

    // Track sub request cancellation
    trackEvent(EVENTS.CANCEL_SUB_REQUEST, {
      sub_request_id: id,
      class_name: className,
      request_date: requestDate,
    });
  });

  return (
    <ModalBottomSheet
      isVisible={isOpen}
      onClose={() => setIsOpen(false)}
      title="Cancel Sub Request?"
      height={300}
    >
      <View className="flex-1">
        <View className="mb-6">
          <Text
            className="text-center text-red-500 font-medium"
            accessibilityLabel="Are you sure you want to cancel this sub request? It cannot be undone"
            accessibilityRole="text"
          >
            Are you sure you want to cancel this sub request? It cannot be
            undone.
          </Text>
        </View>

        <View className="flex-row gap-3 mt-4">
          <Button
            onPress={() => setIsOpen(false)}
            variant="secondary"
            className="flex-1"
            label="No, Keep Request"
            accessibilityLabel="Keep sub request"
            accessibilityRole="button"
            accessibilityHint="Keeps the sub request and closes this dialog"
          />
          <Button
            onPress={() => handleCancellation(id)}
            disabled={isPending}
            variant="destructive"
            className="flex-1"
            label={isPending ? "Cancelling..." : "Yes, Cancel Request"}
            accessibilityLabel={
              isPending ? "Cancelling sub request" : "Cancel sub request"
            }
            accessibilityRole="button"
            accessibilityHint="Permanently cancels this sub request"
          />
        </View>
      </View>
    </ModalBottomSheet>
  );
}
