import { View } from "react-native";
import { But<PERSON> } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useICanSub } from "~/modules/sub-management/mutations/useICanSub";
import { useAnalytics } from "~/modules/hooks/useAnalytics";
import { useHomeSubOptimisticUpdate } from "~/modules/sub-management/queries/useHomeSubOptimisticUpdate";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";

export function HomeCreateSubRequestModal({
  isOpen,
  setIsOpen,
  id,
  className,
  requestDate,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  id: number;
  className?: string;
  requestDate?: string;
}) {
  const { trackEvent, EVENTS } = useAnalytics();
  const { refreshSubRequests } = useHomeSubOptimisticUpdate();

  const handleSuccess = () => {
    setIsOpen(false);

    // Force refresh the sub requests data
    refreshSubRequests();

    // Track sub request acceptance
    trackEvent(EVENTS.ACCEPT_SUB_REQUEST, {
      sub_request_id: id,
      class_name: className,
      request_date: requestDate,
    });
  };

  const { mutate: handleICanSub, isPending } = useICanSub(handleSuccess);

  return (
    <ModalBottomSheet
      isVisible={isOpen}
      onClose={() => setIsOpen(false)}
      title="Request to Sub"
      height={300}
    >
      <View className="flex-1">
        <View className="mb-6">
          <Text
            className="text-center text-base font-medium"
            accessibilityLabel={`Are you sure you want to request to sub ${
              className ? `the ${className} class` : "this class"
            }?`}
            accessibilityRole="text"
          >
            Are you sure you want to request to sub
            {className ? ` the ${className}` : " this"} class?
          </Text>
        </View>

        <View className="flex-row gap-3 mt-4">
          <Button
            onPress={() => setIsOpen(false)}
            variant="secondary"
            className="flex-1"
            label="No, Cancel"
            accessibilityLabel="Cancel sub request"
            accessibilityRole="button"
            accessibilityHint="Cancels the sub request and closes this dialog"
          />
          <Button
            onPress={() => {
              handleICanSub(id);
            }}
            disabled={isPending}
            className="flex-1 bg-[#002966]"
            label={isPending ? "Sending..." : "Yes, Send Request"}
            accessibilityLabel={
              isPending ? "Sending sub request" : "Confirm and send sub request"
            }
            accessibilityRole="button"
            accessibilityHint="Confirms your request to substitute for this class"
          />
        </View>
      </View>
    </ModalBottomSheet>
  );
}
