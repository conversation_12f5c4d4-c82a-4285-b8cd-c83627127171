import { TouchableOpacity, View } from "react-native";
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "~/components/ui/card";
import { Text } from "~/components/ui/text";

import { obtainDateFrame } from "~/modules/classes/utils";
import { SubRequest } from "./types";
import { format } from "date-fns";

import FontAwesome from "@expo/vector-icons/FontAwesome";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { useState } from "react";
import { useSession } from "~/modules/login/auth-provider";
import { HomeCreateSubRequestModal } from "./home-create-sub-modal";
import { useHomeSubOptimisticUpdate } from "~/modules/sub-management/queries/useHomeSubOptimisticUpdate";

const obtainIconAndLabel = ({
  isApproved,
  isPending,
}: {
  isApproved: boolean;
  isPending?: boolean;
}) => {
  if (isApproved) {
    return {
      label: "Approved",
      icon: <MaterialIcons name="approval" size={20} color="white" />,
      color: "green",
      isDisabled: true,
    };
  }

  if (isPending)
    return {
      label: "Pending Approval",
      icon: <FontAwesome name="hourglass-2" size={20} color="white" />,
      color: "#F79720",
      isDisabled: true,
    };

  return {
    label: "Sub?",
    icon: <FontAwesome name="hand-stop-o" size={20} color="white" />,
  };
};

const DEFAULT_DATE_FORMAT = "E, MMM d";

export function HomeSubCard({
  start_time,
  end_time,
  class_name,
  request_date,
  is_approved,
  i_can_sub_instructors,
  gym_name,
  id,
  urgency,
  requesting_instructor_first_name,
  requesting_instructor_last_name,
}: SubRequest) {
  const formattedDate = format(new Date(request_date), DEFAULT_DATE_FORMAT);

  const { data: sessionData } = useSession();
  const { refreshSubRequests } = useHomeSubOptimisticUpdate();

  const [openModal, setOpenModal] = useState(false);

  const { label, icon, color, isDisabled } = obtainIconAndLabel({
    isApproved: Boolean(is_approved),
    isPending: i_can_sub_instructors?.some(
      (user) => user.id === sessionData?.id
    ),
  });

  // Custom handler for opening the modal
  const handleOpenModal = () => {
    setOpenModal(true);
  };

  // Custom handler for closing the modal with refresh
  const handleCloseModal = (shouldRefresh: boolean) => {
    setOpenModal(false);
    if (shouldRefresh) {
      // Force refresh the sub requests data
      refreshSubRequests();
    }
  };

  return (
    <View className="mb-2">
      <Card className="w-full dark:border-white">
        <CardContent className="flex flex-row justify-between items-stretch p-0">
          <View className="flex flex-col gap-0.5 flex-1 p-3">
            <View className="flex flex-row items-center">
              {urgency === "URGENT" && (
                <CardDescription className="mr-1">
                  <FontAwesome
                    name="exclamation-triangle"
                    style={{ fontSize: 12, fontWeight: "800" }}
                    size={12}
                    color="#F41E69"
                  />
                </CardDescription>
              )}
              <CardDescription className="pb-0 text-sm">
                {`${formattedDate} . ${obtainDateFrame(start_time, end_time)}`}
              </CardDescription>
            </View>

            <CardTitle className="pb-0 text-base">{class_name}</CardTitle>

            <CardDescription className="pb-0 text-black dark:text-white text-sm">
              {gym_name}
            </CardDescription>
            <CardDescription className="pb-0 text-black dark:text-white text-sm font-bold">
              {requesting_instructor_first_name}{" "}
              {requesting_instructor_last_name}
            </CardDescription>
          </View>

          <TouchableOpacity
            disabled={isDisabled}
            onPress={handleOpenModal}
            style={{ backgroundColor: color || "#069CC3" }}
            className="w-16 flex items-center justify-center rounded-r-md shadow-sm border-l border-white/10"
            accessibilityLabel={`Sub for ${class_name}?`}
            accessibilityRole="button"
            accessibilityHint={`Opens dialog to request substitution for the ${class_name} class`}
            accessibilityState={{ disabled: isDisabled }}
          >
            <Text
              style={{ fontSize: 10, fontWeight: 700 }}
              className="text-white whitespace-nowrap mr-1"
            >
              {label}
            </Text>
            {React.cloneElement(icon, { size: 16 })}
          </TouchableOpacity>
        </CardContent>
      </Card>
      {openModal && (
        <HomeCreateSubRequestModal
          id={id}
          setIsOpen={(isOpen) => handleCloseModal(!isOpen)}
          isOpen={openModal}
          className={class_name}
          requestDate={request_date}
        />
      )}
    </View>
  );
}
