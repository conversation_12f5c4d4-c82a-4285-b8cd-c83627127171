import React from "react";
import { View } from "react-native";
import { TouchableOpacity } from "react-native-gesture-handler";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { FormInput, FormLabel, FormMessage } from "~/components/ui/form";
import { format, getTime } from "date-fns";

import Fontisto from "@expo/vector-icons/Fontisto";
import { useColorScheme } from "~/lib/useColorScheme";
import { noop } from "lodash/fp";

export const TimePicker = ({
  label,
  value,
  onChange,
  selectedDate,
}: {
  label: string;
  value: Date;
  onChange: (date: Date) => void;
  selectedDate: Date;
}) => {
  const { isDarkColorScheme } = useColorScheme();

  const [isDatePickerVisible, setDatePickerVisibility] = React.useState(false);

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date: Date) => {
    onChange(date);
    hideDatePicker();
  };

  const minimumDate = getTime(new Date()) < getTime(value) ? new Date() : value;

  return (
    <React.Fragment>
      <FormLabel>{label}</FormLabel>
      <View className="mb-2">
        <FormInput
          name=""
          textContentType="oneTimeCode"
          value={format(value, "h:mm a")}
          onBlur={noop}
          onChange={noop}
          className="p-4"
          onPress={showDatePicker}
          style={{ height: 50 }}
        />

        <TouchableOpacity
          onPress={showDatePicker}
          style={{
            position: "absolute",
            right: 10,
            top: "50%",
            transform: [{ translateY: -40 }],
          }}
        >
          <Fontisto
            name="clock"
            size={20}
            color={isDarkColorScheme ? "white" : "dark"}
            onPress={showDatePicker}
          />
        </TouchableOpacity>
        <DateTimePickerModal
          date={value}
          minimumDate={selectedDate > new Date() ? undefined : minimumDate}
          isDarkModeEnabled={isDarkColorScheme}
          minuteInterval={10}
          isVisible={isDatePickerVisible}
          mode={"time"}
          onConfirm={handleConfirm}
          onCancel={hideDatePicker}
          confirmTextIOS="Confirm"
          cancelTextIOS=""
          customCancelButtonIOS={() => <></>}
        />
        <FormMessage />
      </View>
    </React.Fragment>
  );
};
