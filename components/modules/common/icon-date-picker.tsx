import React from "react";
import { View } from "react-native";
import { TouchableOpacity } from "react-native-gesture-handler";
import DateTimePickerModal from "react-native-modal-datetime-picker";

import Fontisto from "@expo/vector-icons/Fontisto";
import { useColorScheme } from "~/lib/useColorScheme";

export const IconDatePicker = ({
  value,
  onChange,
  minDate = new Date(),
  mode = "date",
}: {
  value: Date;
  onChange: (date: Date) => void;
  minDate?: Date;
  mode?: "date" | "time" | "datetime";
}) => {
  const { isDarkColorScheme } = useColorScheme();

  const [isDatePickerVisible, setDatePickerVisibility] = React.useState(false);

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date: Date) => {
    onChange(date);
    hideDatePicker();
  };

  return (
    <React.Fragment>
      <View className="mb-2">
        <TouchableOpacity onPress={showDatePicker} style={{}}>
          <Fontisto
            name="date"
            accessibilityLabel="Open date picker"
            accessibilityRole="button"
            accessibilityHint="Opens date picker to select a new date"
            size={20}
            color={"orange"}
            onPress={showDatePicker}
          />
        </TouchableOpacity>
        <DateTimePickerModal
          date={value}
          locale="en_GB"
          isDarkModeEnabled={isDarkColorScheme}
          minimumDate={minDate}
          display="inline"
          isVisible={isDatePickerVisible}
          mode={mode}
          onConfirm={handleConfirm}
          onCancel={hideDatePicker}
          confirmTextIOS="Confirm"
          cancelTextIOS=""
          customCancelButtonIOS={() => <></>}
          textColor={isDarkColorScheme ? "white" : "black"} // Force text color
        />
      </View>
    </React.Fragment>
  );
};
