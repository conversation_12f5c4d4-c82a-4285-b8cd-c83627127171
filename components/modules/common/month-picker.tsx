import React, { useState, ReactNode, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  Platform,
  ScrollView,
  TextStyle,
  StyleProp,
} from "react-native";
import { Text } from "~/components/ui/text";
import { ChevronLeft, ChevronRight, Calendar } from "lucide-react-native";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";
import { Button } from "~/components/ui/button";
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  withSpring,
} from "react-native-reanimated";
import {
  format,
  setYear,
  setMonth,
  getYear,
  getMonth,
  eachMonthOfInterval,
  startOfYear,
  endOfYear,
  isSameMonth,
  isSameYear,
} from "date-fns";

interface Theme {
  primary: string;
  background: string;
  text: string;
  textSecondary: string;
  border: string;
  selected: string;
  selectedText: string;
}

interface MonthDatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  minYear?: number;
  maxYear?: number;
  trigger?: ReactNode;
  triggerContainerStyle?: string;
  triggerTextStyle?: string;
  theme?: Partial<Theme>;
  formatDate?: (date: Date) => string;
  modalTitle?: string;
  modalTitleStyle?: StyleProp<TextStyle>;
  monthFormat?: "short" | "long";
  showIcon?: boolean;
  iconColor?: string;
  iconSize?: number;
}

const defaultTheme: Theme = {
  primary: "#3b82f6",
  background: "#ffffff",
  text: "#1f2937",
  textSecondary: "#4b5563",
  border: "#f3f4f6",
  selected: "#3b82f6",
  selectedText: "#ffffff",
};

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

export function MonthDatePicker({
  value,
  onChange,
  minYear = getYear(new Date()) - 2,
  maxYear = getYear(new Date()) + 5,
  trigger,
  theme: customTheme,
  modalTitle = "Select Date",
  modalTitleStyle,
  monthFormat = "short",
  showIcon = true,
  iconSize = 20,
}: MonthDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(value);
  const [previewDate, setPreviewDate] = useState(value);
  const [showYearPicker, setShowYearPicker] = useState(false);

  const theme: Theme = { ...defaultTheme, ...customTheme };

  const years = Array.from(
    { length: maxYear - minYear + 1 },
    (_, i) => minYear + i
  );

  useEffect(() => {
    setPreviewDate(value);
  }, [value]);

  const months = eachMonthOfInterval({
    start: startOfYear(previewDate),
    end: endOfYear(previewDate),
  });

  const handleYearSelect = (year: number) => {
    const newDate = setYear(previewDate, year);
    setPreviewDate(newDate);
    setShowYearPicker(false);
  };

  const navigateYear = (direction: "prev" | "next") => {
    const currentYear = getYear(previewDate);
    const newYear = direction === "prev" ? currentYear - 1 : currentYear + 1;

    if (direction === "prev" && newYear < minYear) return;
    if (direction === "next" && newYear > maxYear) return;

    const newDate = setYear(previewDate, newYear);
    setPreviewDate(newDate);
  };

  const yearButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: withSpring(showYearPicker ? 1.1 : 1) }],
  }));

  const handleClose = () => {
    setIsOpen(false);
    setShowYearPicker(false);
    setPreviewDate(selectedDate);
  };

  const handleMonthSelect = (monthIndex: number) => {
    const newDate = setMonth(previewDate, monthIndex);
    setPreviewDate(newDate);
  };

  const handleConfirm = () => {
    setSelectedDate(previewDate);
    onChange(previewDate);
    handleClose();
  };

  const renderTrigger = () => {
    const currentDateLabel = format(selectedDate, "MMMM yyyy");

    if (trigger) {
      return (
        <TouchableOpacity
          onPress={() => setIsOpen(true)}
          accessibilityRole="button"
          accessibilityLabel={`Open month picker, current selection is ${currentDateLabel}`}
          accessibilityHint="Opens month and year picker to select a new date"
        >
          {trigger}
        </TouchableOpacity>
      );
    }

    return (
      <TouchableOpacity
        onPress={() => setIsOpen(true)}
        accessibilityRole="button"
        accessibilityLabel={`Open month picker, current selection is ${currentDateLabel}`}
        accessibilityHint="Opens month and year picker to select a new date"
      >
        {showIcon && <Calendar size={24} color={"orange"} className="mr-2" />}
      </TouchableOpacity>
    );
  };

  return (
    <>
      {renderTrigger()}

      <ModalBottomSheet
        isVisible={isOpen}
        onClose={handleClose}
        title={modalTitle || "Select Date"}
        height={450}
      >
        <ScrollView className="flex-1">
          <View className="flex-row justify-between items-center mb-4">
            <TouchableOpacity
              onPress={() => navigateYear("prev")}
              className="p-2"
              accessibilityRole="button"
              accessibilityLabel="Previous year"
              accessibilityHint={`Navigate to ${getYear(previewDate) - 1}`}
            >
              <ChevronLeft size={20} color={theme.textSecondary} />
            </TouchableOpacity>

            <AnimatedTouchableOpacity
              onPress={() => setShowYearPicker(!showYearPicker)}
              className="px-4 py-2"
              style={yearButtonStyle}
              accessibilityRole="button"
              accessibilityLabel={`Current year: ${format(
                previewDate,
                "yyyy"
              )}`}
              accessibilityHint={
                showYearPicker
                  ? "Hide year picker"
                  : "Show year picker to select a different year"
              }
            >
              <Text className="text-base font-semibold text-gray-900">
                {format(previewDate, "yyyy")}
              </Text>
            </AnimatedTouchableOpacity>

            <TouchableOpacity
              onPress={() => navigateYear("next")}
              className="p-2"
              accessibilityRole="button"
              accessibilityLabel="Next year"
              accessibilityHint={`Navigate to ${getYear(previewDate) + 1}`}
            >
              <ChevronRight size={20} color={theme.textSecondary} />
            </TouchableOpacity>
          </View>

          {!showYearPicker ? (
            <View className="mb-6">
              <Text
                className="text-sm font-medium mb-3 text-gray-700"
                accessibilityRole="header"
                accessibilityLabel="Select month"
              >
                Select Month
              </Text>
              <View className="flex-row flex-wrap gap-2">
                {months.map((month) => (
                  <TouchableOpacity
                    key={format(month, "MMM")}
                    onPress={() => handleMonthSelect(getMonth(month))}
                    style={{
                      width: "22%",
                      height: 50,
                      backgroundColor: isSameMonth(previewDate, month)
                        ? "#002966"
                        : "#f3f4f6",
                      borderRadius: 8,
                      justifyContent: "center",
                      alignItems: "center",
                      marginBottom: 8,
                    }}
                    accessibilityRole="button"
                    accessibilityLabel={`${format(month, "MMMM")} ${format(
                      previewDate,
                      "yyyy"
                    )}`}
                    accessibilityHint={`Select ${format(
                      month,
                      "MMMM"
                    )} as the month`}
                    accessibilityState={{
                      selected: isSameMonth(previewDate, month),
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 12,
                        fontWeight: "600",
                        color: isSameMonth(previewDate, month)
                          ? "#ffffff"
                          : "#374151",
                      }}
                    >
                      {monthFormat === "short"
                        ? format(month, "MMM").toUpperCase()
                        : format(month, "MMMM")}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ) : (
            <View className="mb-6">
              <Text
                className="text-sm font-medium mb-3 text-gray-700"
                accessibilityRole="header"
                accessibilityLabel="Select year"
              >
                Select Year
              </Text>
              <View
                style={{ height: 200 }}
                className="rounded-lg overflow-hidden border border-gray-200"
              >
                <ScrollView
                  className="flex-1"
                  showsVerticalScrollIndicator={true}
                  accessibilityLabel="Year list"
                >
                  {years.map((year) => (
                    <TouchableOpacity
                      key={year}
                      onPress={() => handleYearSelect(year)}
                      style={{
                        padding: 12,
                        alignItems: "center",
                        marginBottom: 4,
                        borderRadius: 8,
                        marginHorizontal: 8,
                        backgroundColor: isSameYear(
                          previewDate,
                          new Date(year, 0)
                        )
                          ? "#002966"
                          : "#f9fafb",
                      }}
                      accessibilityRole="button"
                      accessibilityLabel={`Year ${year}`}
                      accessibilityHint={`Select ${year} as the year`}
                      accessibilityState={{
                        selected: isSameYear(previewDate, new Date(year, 0)),
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: "500",
                          color: isSameYear(previewDate, new Date(year, 0))
                            ? "#ffffff"
                            : "#374151",
                        }}
                      >
                        {year}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          )}

          <View className="flex-row gap-3 mt-4">
            <Button
              onPress={handleClose}
              variant="secondary"
              className="flex-1"
              label="Cancel"
              accessibilityLabel="Cancel date selection"
              accessibilityRole="button"
              accessibilityHint="Cancels date selection and closes this picker"
            />
            <Button
              onPress={handleConfirm}
              className="flex-1 bg-[#002966]"
              label="Confirm"
              accessibilityLabel={`Confirm selected date: ${format(
                previewDate,
                "MMMM yyyy"
              )}`}
              accessibilityRole="button"
              accessibilityHint="Confirms the selected month and year"
            />
          </View>
        </ScrollView>
      </ModalBottomSheet>
    </>
  );
}
