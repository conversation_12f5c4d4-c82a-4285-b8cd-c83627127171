import { Alert, View } from "react-native";
import { Card, CardContent } from "~/components/ui/card";
import { Text } from "~/components/ui/text";

import { format, parseISO } from "date-fns";
import { ClassCancellation } from "~/modules/cancelled-classes/types";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { obtainDateFrame } from "~/modules/classes/utils";
import { Button } from "~/components/ui/button";
import { useDeleteCancelledClass } from "~/modules/cancelled-classes/mutations/useDeleteCancelledClass";
import { router } from "expo-router";

export function CancelledClassCard({
  date,
  reason,
  classes = [],
  can_modify,
  id,
}: ClassCancellation) {
  const formattedDate = format(parseISO(date), "EEE MMM d, yyyy");

  const { mutate: deleteCancelledClass } = useDeleteCancelledClass();

  const handleDelete = () => {
    Alert.alert(
      "Confirm Delete",
      `Are you sure you want to delete this cancelled class?`,
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          onPress: async () => {
            await deleteCancelledClass(id);
          },
        },
      ]
    );
  };

  const handleEdit = () =>
    router.push({
      pathname: "/(cancelled)/create",
      params: {
        id,
        date,
        reason,
        classes: JSON.stringify(
          classes.map((cls) => ({
            id: cls.slot_id.toString(),
            title: `${cls.class_name} - ${cls.class_start_time} to ${cls.class_end_time} (${cls.gym_name})`,
          }))
        ),
      },
    });

  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={[]}
      className="w-full max-w-sm native:max-w-md mb-3"
    >
      <Card>
        <AccordionItem value="item-1">
          <AccordionTrigger className="pr-4 p-4">
            <View className="flex flex-col">
              <Text className="font-semibold">{formattedDate}</Text>
              <View className="flex flex-row gap-2 w-[95%]">
                <Text className="font-bold">Class:</Text>
                <Text className="w-[95%] text-gray-700 dark:text-gray-300">
                  {classes.map((classInfo) => classInfo.class_name).join(", ")}
                </Text>
              </View>
            </View>
          </AccordionTrigger>
          <AccordionContent>
            <CardContent>
              {classes.map((classInfo, index) => (
                <View
                  key={index}
                  className="flex flex-row  gap-2 mb-2 border-b border-gray-200 dark:border-gray-700 pb-2"
                >
                  <View>
                    <View className="flex flex-row gap-2">
                      <Text className="font-bold text-gray-700 dark:text-gray-300 mb-1 whitespace-nowrap">
                        Class:
                      </Text>
                      <Text className="text-gray-700 dark:text-gray-300 mb-1">
                        {classInfo.class_name}
                      </Text>
                    </View>
                    <View className="flex flex-row gap-2">
                      <Text className="font-bold text-gray-700 dark:text-gray-300 mb-1 whitespace-nowrap">
                        Instructor:
                      </Text>
                      <Text className="text-gray-700 dark:text-gray-300 mb-1">
                        {`${classInfo.instructor_first_name} ${classInfo.instructor_last_name}`}
                      </Text>
                    </View>

                    <View className="flex flex-row gap-2">
                      <Text className="font-bold text-gray-700 dark:text-gray-300 mb-1 whitespace-nowrap">
                        Gym:
                      </Text>
                      <Text className="text-gray-700 dark:text-gray-300 mb-1">
                        {classInfo.gym_name}
                      </Text>
                    </View>
                    <View className="flex flex-row gap-2">
                      <Text className="font-bold text-gray-700 dark:text-gray-300 mb-1 whitespace-nowrap">
                        Time:
                      </Text>
                      <Text className="text-gray-700 dark:text-gray-300 mb-1">
                        {obtainDateFrame(
                          classInfo.class_start_time,
                          classInfo.class_end_time
                        )}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
              <View className="flex flex-row whitespace-pre-wrap w-[90%]">
                <Text className="font-bold text-gray-700 dark:text-gray-300 mb-1 whitespace-nowrap">
                  Reason:
                </Text>
                <Text className="text-gray-600 dark:text-gray-400 pl-2 whitespace-break-spaces">
                  {reason}
                </Text>
              </View>
              {can_modify && (
                <View className="flex flex-row ml-auto gap-2">
                  <Button
                    onPress={handleEdit}
                    label="Edit"
                    className="bg-[#002966]"
                  />
                  <Button
                    onPress={handleDelete}
                    label="Delete"
                    className="bg-red-500"
                  />
                </View>
              )}
            </CardContent>
          </AccordionContent>
        </AccordionItem>
      </Card>
    </Accordion>
  );
}
