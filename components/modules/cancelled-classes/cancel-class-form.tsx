import React from "react";
import { View, ScrollView } from "react-native";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "~/components/ui/button";
import {
  Form,
  FormField,
  FormLabel,
  FormMessage,
  FormInput,
} from "~/components/ui/form";
import { Text } from "~/components/ui/text";
import { DatePicker } from "~/components/modules/common/date-picker";
import { MultiComboBox } from "~/components/modules/common/Multi-combo-box";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import {
  AutocompleteDropdownContextProvider,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";
import { useClassesData } from "~/modules/classes/queries/useClassesQuery";
import { formatClassDate, formatTime } from "~/modules/classes/utils";
import { useLocalSearchParams } from "expo-router";
import { ClassDetailsResponse } from "~/modules/classes/types";
import { useFocusEffect } from "@react-navigation/native";

export const CancelClassSchema = z.object({
  date: z.date({
    message: "Date is required",
  }),
  classes: z
    .array(
      z.object({
        id: z.string(),
        title: z.string(),
      })
    )
    .min(1, "At least one class must be selected"),
  reason: z.string().min(1, "Reason is required"),
});

export type CancelClassFormValues = z.infer<typeof CancelClassSchema>;

interface CancelClassFormProps {
  onSubmit: (data: CancelClassFormValues) => void;
  isLoading?: boolean;
}

export const formatClassItem = (
  classesData?: ClassDetailsResponse[],
  keyIdentifier: keyof ClassDetailsResponse = "slot_id"
) => {
  if (!classesData) return [];
  return classesData.map((cls) => ({
    id: cls?.[keyIdentifier]?.toString() as string,
    title: `${cls.name} - ${formatTime(cls.start_time)}-${formatTime(
      cls.end_time
    )} (${cls.room_name})`,
  }));
};

export const CancelClassForm = ({
  onSubmit,
  isLoading,
}: CancelClassFormProps) => {
  const { id, date, reason, classes } = useLocalSearchParams<{
    id: string;
    date: string;
    reason: string;
    classes: string;
  }>();

  const form = useForm<CancelClassFormValues>({
    resolver: zodResolver(CancelClassSchema),
    defaultValues: {
      date: date ? new Date(date) : new Date(),
      classes: classes ? JSON.parse(classes) : [],
      reason: reason || "",
    },
  });

  // Reset form when screen comes into focus to ensure clean state
  useFocusEffect(
    React.useCallback(() => {
      if (!id) {
        form.reset({
          date: new Date(),
          classes: [],
          reason: "",
        });
      }
    }, [form, id])
  );

  const selectedValues = useWatch({
    control: form.control,
  });

  const { data: classesData = [], isPending } = useClassesData({
    date: formatClassDate(selectedValues.date),
  });

  const classOptions: AutocompleteDropdownItem[] = formatClassItem(classesData);

  const resetClassesValue = () => {
    form.setValue("classes", [], { shouldDirty: true });
  };

  return (
    <AutocompleteDropdownContextProvider>
      <View className="flex-1">
        <Form {...form}>
          <KeyboardAwareScrollView
            bottomOffset={10}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingTop: 16,
              paddingBottom: 100,
            }}
            className="flex-1"
          >
            <ScrollView className="space-y-4">
              <View className="space-y-4">
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <View className="space-y-3">
                      <DatePicker
                        value={field.value}
                        onChange={(date) => {
                          field.onChange(date);
                          resetClassesValue();
                        }}
                        label="Date*"
                      />
                      <FormMessage />
                    </View>
                  )}
                />

                <FormField
                  control={form.control}
                  name="classes"
                  render={({ field }) => (
                    <View className="space-y-2">
                      <FormLabel>Classes* (select one or more)</FormLabel>
                      <View style={{ minHeight: 50 }}>
                        <MultiComboBox
                          data={classOptions}
                          placeholder="Select classes to cancel"
                          selectedItems={field.value}
                          onSelect={(items: AutocompleteDropdownItem[]) => {
                            field.onChange(items);
                          }}
                        />
                      </View>
                      <FormMessage />
                      {isPending && (
                        <Text className="text-sm text-gray-500">
                          Loading classes...
                        </Text>
                      )}
                    </View>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <View className="space-y-2">
                      <FormLabel>Reason*</FormLabel>
                      <FormInput
                        placeholder="Enter reason for cancellation"
                        value={field.value || ""}
                        onChangeText={field.onChange}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        multiline={true}
                        numberOfLines={4}
                        style={{
                          height: 100,
                          textAlignVertical: "top",
                        }}
                      />
                    </View>
                  )}
                />

                <View className="pt-4">
                  <Button
                    onPress={form.handleSubmit(onSubmit)}
                    disabled={isLoading}
                    className="w-full"
                    label={isLoading ? "Cancelling..." : "Save"}
                    isLoading={isLoading}
                  />
                </View>
              </View>
            </ScrollView>
          </KeyboardAwareScrollView>
        </Form>
      </View>
    </AutocompleteDropdownContextProvider>
  );
};
