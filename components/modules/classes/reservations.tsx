import React, { ReactNode } from "react";
import { View } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { FlashList } from "@shopify/flash-list";
import { ReserveCard } from "./reserve-card";
import { ClassDetailsResponse, Reservation } from "~/modules/classes/types";
import { Text } from "~/components/ui/text";
import { CelebrationChips } from "./celebrations/celebration-chips";
import { CelebrationModalProps } from "./celebrations/celebration";

export function EmptyState({
  label = "No reservations data available",
  action,
}: {
  label?: string;
  action?: ReactNode;
}) {
  return (
    <View className="flex-1 items-center justify-center">
      <Ionicons name="calendar-outline" size={64} color="#94a3b8" />
      <Text className="text-slate-400 text-lg mt-4">{label}</Text>
      {action}
    </View>
  );
}

export interface ReservationProps extends CelebrationModalProps {
  reservations?: Reservation[];
  waitlist?: ClassDetailsResponse[];
}

export function Reservations({ reservations, celebrations }: ReservationProps) {
  return (
    <View className="flex flex-1 h-full overflow-scroll">
      {!reservations?.length ? (
        <EmptyState />
      ) : (
        <FlashList
          data={reservations}
          renderItem={({ item }) => (
            <ReserveCard
              name={`${item?.user_first_name} ${item?.user_last_name}`}
              isCheckIn={Boolean(item?.checkin)}
              id={item.id}
              image={item?.image}
            >
              <CelebrationChips
                celebrations={celebrations}
                userId={item?.user_id}
              />
            </ReserveCard>
          )}
          estimatedItemSize={800}
        />
      )}
    </View>
  );
}
