import {
  DrawerContentScrollView,
  // DrawerItemList,
} from "@react-navigation/drawer";

import { useSession } from "~/modules/login/auth-provider";

import { View, Alert, Pressable } from "react-native";
import Ionicons from "@expo/vector-icons/Ionicons";
import { Text } from "~/components/ui/text";
import { useColorScheme } from "~/lib/useColorScheme";
import { getInitials } from "~/modules/classes/utils";
import { BaseAvatar } from "../classes/avatar";
import { useRouter } from "expo-router";

import appConfig from "../../../app.json";
import { ContactClassIcon } from "~/components/icons/contact-class";
import { CancelledClassIcon } from "~/components/icons/cancelled-class";
import { LogoutIcon } from "~/components/icons/logout";
import { DocumentIcon } from "~/components/icons/document";
import { PermissionGate, PERMISSIONS } from "~/modules/permissions";

export const CustomDrawer = (props: any) => {
  const { signOut, data } = useSession();
  const { isDarkColorScheme } = useColorScheme();
  const router = useRouter();

  // App version from app.json
  const appVersion = appConfig?.expo?.version;

  const handleSignOut = () => {
    Alert.alert("Sign out", "Are you sure you want to sign out?", [
      {
        text: "Cancel",
        style: "cancel",
      },
      { text: "OK", onPress: signOut },
    ]);
  };

  const openTermsAndConditions = () => {
    props.navigation.closeDrawer();
    router.push("/(classes)/terms");
  };

  const openCancelledClasses = () => {
    props.navigation.closeDrawer();
    router.push("/(classes)/(tabs)/cancelled");
  };

  const openContactClasses = () => {
    props.navigation.closeDrawer();
    router.push("/(classes)/(tabs)/contact");
  };

  const userName = data ? `${data.first_name} ${data.last_name}` : "User";
  const userEmail = data?.email || "";

  return (
    <View style={{ flex: 1 }} className="bg-background dark:bg-background">
      <View className="pt-16 pb-6 px-5 bg-[#1A237E]">
        <View className="flex-row items-center">
          <BaseAvatar url={""} name={getInitials(userName)} size={50} />
          <View className="ml-3 flex-1">
            <Text
              className="font-bold text-base text-white"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {userName}
            </Text>
            <Text
              className="text-white text-xs opacity-80"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {userEmail}
            </Text>
          </View>
        </View>
      </View>

      {/* Navigation Items */}
      <DrawerContentScrollView
        {...props}
        scrollEnabled={true}
        contentContainerStyle={{
          paddingTop: 0, // Remove default padding to avoid extra space
        }}
      >
        <View className="mt-4">
          <Text className="text-gray-500 dark:text-gray-400 px-5 mb-2 text-xs uppercase">
            Class Management
          </Text>
          <PermissionGate permission={PERMISSIONS.CLASSES.CONTACT_CLASS.ACCESS}>
            <Pressable
              className="flex-row items-center px-5 py-3"
              android_ripple={{ color: "rgba(0, 0, 0, 0.1)" }}
              onPress={openContactClasses}
              accessibilityRole="button"
              accessibilityLabel="Contact Classes"
              accessibilityHint="Navigate to contact classes"
            >
              <ContactClassIcon
                color={isDarkColorScheme ? "white" : "orange"}
              />
              <Text className="ml-3 dark:text-white">Contact Classes</Text>
            </Pressable>
          </PermissionGate>
        </View>

        {/* <DrawerItemList {...props} /> */}

        {/* Cancelled Classes */}
        <PermissionGate permission={PERMISSIONS.CLASSES.CANCEL_CLASS.ACCESS}>
          <Pressable
            className="flex-row items-center px-5 py-3"
            android_ripple={{ color: "rgba(0, 0, 0, 0.1)" }}
            onPress={openCancelledClasses}
            accessibilityRole="button"
            accessibilityLabel="Cancelled Classes"
            accessibilityHint="Navigate to cancelled classes"
          >
            <CancelledClassIcon
              color={isDarkColorScheme ? "white" : "orange"}
            />
            <Text className="ml-3 dark:text-white">Cancel Classes</Text>
          </Pressable>
        </PermissionGate>

        {/* Account Section */}
        <View className="mt-4">
          <Text className="text-gray-500 dark:text-gray-400 px-5 mb-2 text-xs uppercase">
            Account
          </Text>

          {/* Terms & Conditions */}
          <Pressable
            className="flex-row items-center px-5 py-3"
            android_ripple={{ color: "rgba(0, 0, 0, 0.1)" }}
            onPress={openTermsAndConditions}
          >
            <DocumentIcon />
            <Text className="ml-3 dark:text-white">Terms & Conditions</Text>
          </Pressable>

          {/* Logout */}
          <Pressable
            className="flex-row items-center px-5 py-3"
            android_ripple={{ color: "rgba(0, 0, 0, 0.1)" }}
            onPress={handleSignOut}
          >
            <LogoutIcon className="w-6 h-6" />
            <Text className="ml-3 dark:text-white">Logout</Text>
          </Pressable>
        </View>
      </DrawerContentScrollView>

      {/* App Version */}
      <View className="border-t-gray-200 border-t-2 p-5 pb-5 items-center">
        <Text className="text-gray-500 dark:text-gray-400 text-xs">
          Version {appVersion}
        </Text>
      </View>
    </View>
  );
};
