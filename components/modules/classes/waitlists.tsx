import React, { Fragment, useState } from "react";
import { TouchableOpacity, View } from "react-native";

import { FlashList } from "@shopify/flash-list";

import { ClassDetailsResponse } from "~/modules/classes/types";
import { Text } from "~/components/ui/text";
import { Card, CardContent } from "~/components/ui/card";
import { BaseAvatar } from "./avatar";
import { getInitials } from "~/modules/classes/utils";

import { AddWaitlistToClassModal } from "./add-waitlist-to-class-modal";

import AntDesign from "@expo/vector-icons/AntDesign";
import { EmptyState } from "./reservations";

const WaitlistCard = ({
  name,
  id,
  image,
  position,
}: {
  name: string;
  id: number;
  image?: string;
  position?: number;
}) => {
  const [openModal, setOpenModal] = useState(false);

  return (
    <Fragment>
      <Card
        className="w-full mt-4 mb-3"
        accessibilityLabel={`Waitlist member: ${name}${
          position ? `, position ${position}` : ""
        }`}
      >
        <CardContent className="p-0 flex flex-row justify-between">
          <View
            className="flex flex-row ml-0  mt-2 pl-2 p-1"
            accessibilityLabel={`Member information for ${name}`}
          >
            <BaseAvatar
              url={image}
              name={getInitials(name)}
              accessibilityLabel={`Profile picture for ${name}`}
            />
            <Text
              className="font-bold mt-2 pl-2"
              accessibilityRole="text"
              accessibilityLabel={`Member name: ${name}`}
            >
              {name}
            </Text>
          </View>
          <View
            className={"flex flex-row items-center gap-2"}
            accessibilityLabel={`Actions for ${name}: ${
              position ? `waitlist position ${position}, ` : ""
            }add to class`}
          >
            {position && (
              <View
                className="w-8 h-8 bg-[#1A237E] rounded-full flex items-center justify-center"
                accessibilityRole="text"
                accessibilityLabel={`Waitlist position: ${position}`}
              >
                <Text
                  className="text-white text-sm font-bold"
                  accessibilityLabel={`Position ${position}`}
                >
                  {position}
                </Text>
              </View>
            )}
            <TouchableOpacity
              onPress={() => setOpenModal(true)}
              className="px-4 bg-[#E4E7EC] h-full flex items-center justify-center"
              accessibilityRole="button"
              accessibilityLabel={`Add ${name} to class`}
              accessibilityHint="Opens dialog to add this member from waitlist to the class"
            >
              <AntDesign name="plus" size={20} color="black" />
            </TouchableOpacity>
          </View>
        </CardContent>
      </Card>
      {openModal && (
        <AddWaitlistToClassModal
          isOpen={openModal}
          setIsOpen={setOpenModal}
          waitlistId={id}
        />
      )}
    </Fragment>
  );
};

export function Waitlist({ waitlist }: { waitlist?: ClassDetailsResponse[] }) {
  return (
    <View className="flex flex-1">
      {!waitlist?.length ? (
        <EmptyState label="No waitlist data available" />
      ) : (
        <FlashList
          className="h-full overflow-scroll"
          data={waitlist}
          renderItem={({ item }) => (
            <WaitlistCard
              name={`${item?.first_name} ${item?.last_name}`}
              id={item.id}
              image={item?.image}
              position={item?.position}
            />
          )}
          estimatedItemSize={800}
        />
      )}
    </View>
  );
}
