import { View } from "react-native";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useWaitlistToClass } from "~/modules/classes/queries/useWaitlistToClass";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";

export function AddWaitlistToClassModal({
  isOpen,
  setIsOpen,
  waitlistId,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  waitlistId: number;
}) {
  const { mutate: handlePromoteToClass, isPending } = useWaitlistToClass(() => {
    setIsOpen(false);
  });

  return (
    <ModalBottomSheet
      isVisible={isOpen}
      onClose={() => setIsOpen(false)}
      title="Add User to the Class?"
      height={350}
    >
      <View className="flex-1">
        <View className="mb-6">
          <Text
            className="text-center"
            accessibilityLabel="This will move the user off the waitlist to the class regardless of the spots available"
            accessibilityRole="text"
          >
            This will move the user off the waitlist to the class regardless of
            the spots available. Do you want to proceed?
          </Text>
        </View>

        <View className="flex-row gap-4 mt-4">
          <Button
            onPress={() => setIsOpen(false)}
            variant="secondary"
            className="flex-1"
            label="Close"
            accessibilityLabel="Cancel adding user to class"
            accessibilityRole="button"
            accessibilityHint="Cancels adding the user and closes this dialog"
          />
          <Button
            onPress={() => handlePromoteToClass(waitlistId)}
            disabled={isPending}
            className="flex-1"
            label={isPending ? "Adding..." : "Add to Class"}
            accessibilityLabel={
              isPending ? "Adding user to class" : "Add user to class"
            }
            accessibilityRole="button"
            accessibilityHint="Moves the user from waitlist to the class"
          />
        </View>
      </View>
    </ModalBottomSheet>
  );
}
