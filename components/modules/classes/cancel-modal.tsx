import { View } from "react-native";
import { But<PERSON> } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useCancelReservation } from "~/modules/classes/mutations/useCancelReservation";
import { ModalBottomSheet } from "~/components/modules/common/modal-bottom-sheet";

export function CancelModal({
  isOpen,
  setIsOpen,
  classId,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  classId: number;
}) {
  const { mutate: handleCancellation, isPending } = useCancelReservation(() => {
    setIsOpen(false);
  });

  return (
    <ModalBottomSheet
      isVisible={isOpen}
      onClose={() => setIsOpen(false)}
      title="Cancel Reservation?"
      height={300}
    >
      <View className="flex-1">
        <View className="mb-6">
          <Text
            className="text-center text-red-500 font-medium"
            accessibilityLabel="This will cancel the reservation permanently and cannot be undone"
            accessibilityRole="text"
          >
            This will cancel the reservation permanently and cannot be undone.
            Proceed?
          </Text>
        </View>

        <View className="flex-row gap-3 mt-4">
          <Button
            onPress={() => setIsOpen(false)}
            variant="secondary"
            className="flex-1"
            label="Close"
            accessibilityLabel="Keep reservation"
            accessibilityRole="button"
            accessibilityHint="Keeps the reservation and closes this dialog"
          />
          <Button
            onPress={() => handleCancellation(classId)}
            disabled={isPending}
            variant="destructive"
            className="flex-1"
            label={isPending ? "Cancelling..." : "Cancel Reservation"}
            accessibilityLabel={
              isPending ? "Cancelling reservation" : "Cancel reservation"
            }
            accessibilityRole="button"
            accessibilityHint="Permanently cancels this reservation"
          />
        </View>
      </View>
    </ModalBottomSheet>
  );
}
