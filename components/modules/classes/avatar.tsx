import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Text } from "~/components/ui/text";
import { StyleSheet } from "react-native";

export function BaseAvatar({
  url,
  name,
  size,
  accessibilityLabel,
}: {
  url?: string;
  name: string;
  size?: number;
  accessibilityLabel?: string;
}) {
  const styles = StyleSheet.create({
    avatar: size
      ? {
          width: size,
          height: size,
        }
      : {},
  });

  return (
    <Avatar
      alt={accessibilityLabel || `Avatar for ${name}`}
      style={styles.avatar}
      accessibilityRole="image"
      accessibilityLabel={accessibilityLabel || `Profile picture for ${name}`}
    >
      <AvatarImage
        source={{ uri: url }}
        accessibilityLabel={accessibilityLabel || `Profile picture for ${name}`}
      />
      <AvatarFallback accessibilityLabel={`Profile initials: ${name}`}>
        <Text
          style={{ fontSize: size ? size / 3 : undefined }}
          accessibilityLabel={`Profile initials: ${name}`}
        >
          {name}
        </Text>
      </AvatarFallback>
    </Avatar>
  );
}
