{"name": "upace-instructor", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c --ios", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css"}, "dependencies": {"@babel/runtime": "^7.27.6", "@hookform/resolvers": "^3.9.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-firebase/analytics": "^21.13.0", "@react-native-firebase/app": "^21.13.0", "@react-native-picker/picker": "2.11.1", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.6", "@rn-primitives/accordion": "^1.2.0", "@rn-primitives/avatar": "~1.1.0", "@rn-primitives/checkbox": "^1.1.0", "@rn-primitives/dialog": "^1.1.0", "@rn-primitives/label": "^1.1.0", "@rn-primitives/portal": "~1.1.0", "@rn-primitives/progress": "~1.1.0", "@rn-primitives/select": "^1.1.0", "@rn-primitives/slot": "^1.2.0", "@rn-primitives/switch": "^1.1.0", "@rn-primitives/tabs": "^1.1.0", "@rn-primitives/tooltip": "~1.1.0", "@rn-primitives/types": "^1.1.0", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "2.0.0-next.4", "@tanstack/query-sync-storage-persister": "^5.59.16", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-persist-client": "^5.59.16", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "expo": "^53.0.0", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-calendar": "~14.1.4", "expo-camera": "~16.1.10", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-image": "~2.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-modules-core": "~2.4.2", "expo-navigation-bar": "~4.2.7", "expo-notifications": "~0.31.4", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-task-manager": "~13.1.6", "i": "^0.3.7", "ky": "^1.7.5", "lodash": "^4.17.21", "lottie-react-native": "7.2.2", "lucide-react-native": "^0.525.0", "match-sorter": "^7.0.0", "nativewind": "^4.0.33", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.53.1", "react-native": "0.79.5", "react-native-autocomplete-dropdown": "^4.0.0", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.17.5", "react-native-mmkv": "2.12.2", "react-native-modal-datetime-picker": "^18.0.0", "react-native-reanimated": "~3.17.4", "react-native-root-toast": "^3.6.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.0", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "^19.1.0", "@types/lodash": "^4.17.13", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"enabled": false}, "appConfigFieldsNotSyncedCheck": {"enabled": false}}}, "private": true}